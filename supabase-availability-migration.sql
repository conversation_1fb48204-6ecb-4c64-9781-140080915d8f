-- CAF Employee Availability System - Database Migration
-- Run this AFTER the main employee migration to add availability functionality
-- This enhances the existing employee system with detailed scheduling capabilities

-- ========================================
-- EMPLOYEE AVAILABILITY SYSTEM
-- ========================================

-- Create employee_availability table for detailed scheduling
CREATE TABLE IF NOT EXISTS employee_availability (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0=Sunday, 1=Monday, ..., 6=Saturday
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT true,
    break_start_time TIME, -- Optional break time
    break_end_time TIME,   -- Optional break end time
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure end_time is after start_time
    CONSTRAINT valid_time_range CHECK (end_time > start_time),
    -- Ensure break times are valid if specified
    CONSTRAINT valid_break_times CHECK (
        (break_start_time IS NULL AND break_end_time IS NULL) OR
        (break_start_time IS NOT NULL AND break_end_time IS NOT NULL AND 
         break_end_time > break_start_time AND
         break_start_time >= start_time AND break_end_time <= end_time)
    ),
    -- Prevent overlapping availability slots for same employee/day
    UNIQUE(employee_id, day_of_week, start_time, end_time)
);

-- Create employee_special_schedules table for exceptions (holidays, special hours, etc.)
CREATE TABLE IF NOT EXISTS employee_special_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    is_available BOOLEAN DEFAULT false, -- Default to unavailable for special dates
    start_time TIME,
    end_time TIME,
    break_start_time TIME,
    break_end_time TIME,
    reason VARCHAR(255), -- Holiday, sick leave, special hours, etc.
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure end_time is after start_time when available
    CONSTRAINT valid_special_time_range CHECK (
        NOT is_available OR (is_available AND end_time > start_time)
    ),
    -- Ensure break times are valid if specified
    CONSTRAINT valid_special_break_times CHECK (
        (break_start_time IS NULL AND break_end_time IS NULL) OR
        (break_start_time IS NOT NULL AND break_end_time IS NOT NULL AND 
         break_end_time > break_start_time AND
         break_start_time >= start_time AND break_end_time <= end_time)
    ),
    -- One record per employee per date
    UNIQUE(employee_id, date)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_employee_availability_employee_day ON employee_availability(employee_id, day_of_week);
CREATE INDEX IF NOT EXISTS idx_employee_availability_day_time ON employee_availability(day_of_week, start_time, end_time);
CREATE INDEX IF NOT EXISTS idx_employee_special_schedules_employee_date ON employee_special_schedules(employee_id, date);
CREATE INDEX IF NOT EXISTS idx_employee_special_schedules_date ON employee_special_schedules(date);

-- Enable Row Level Security
ALTER TABLE employee_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_special_schedules ENABLE ROW LEVEL SECURITY;

-- Create policies for employee_availability table
CREATE POLICY "Allow anon to read employee availability" ON employee_availability
    FOR SELECT TO anon
    USING (true);

CREATE POLICY "Allow anon full access to employee availability" ON employee_availability
    FOR ALL TO anon
    USING (true)
    WITH CHECK (true);

-- Create policies for employee_special_schedules table
CREATE POLICY "Allow anon to read employee special schedules" ON employee_special_schedules
    FOR SELECT TO anon
    USING (true);

CREATE POLICY "Allow anon full access to employee special schedules" ON employee_special_schedules
    FOR ALL TO anon
    USING (true)
    WITH CHECK (true);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_employee_availability_updated_at 
    BEFORE UPDATE ON employee_availability 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employee_special_schedules_updated_at 
    BEFORE UPDATE ON employee_special_schedules 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT ALL ON employee_availability TO anon, authenticated;
GRANT ALL ON employee_special_schedules TO anon, authenticated;

-- Insert default availability for existing employees (Monday-Friday, 9:00-18:00 with lunch break)
INSERT INTO employee_availability (employee_id, day_of_week, start_time, end_time, break_start_time, break_end_time, is_available)
SELECT 
    e.id,
    generate_series(1, 5) as day_of_week, -- Monday to Friday
    '09:00'::TIME as start_time,
    '18:00'::TIME as end_time,
    '12:00'::TIME as break_start_time,
    '15:00'::TIME as break_end_time,
    true as is_available
FROM employees e
WHERE e.name != 'Qualsiasi' -- Skip the "any employee" option
ON CONFLICT (employee_id, day_of_week, start_time, end_time) DO NOTHING;

-- ========================================
-- UTILITY VIEWS AND FUNCTIONS
-- ========================================

-- Create view for employee availability with day names
CREATE OR REPLACE VIEW employee_availability_view AS
SELECT 
    ea.id,
    ea.employee_id,
    e.name as employee_name,
    ea.day_of_week,
    CASE ea.day_of_week
        WHEN 0 THEN 'Domenica'
        WHEN 1 THEN 'Lunedì'
        WHEN 2 THEN 'Martedì'
        WHEN 3 THEN 'Mercoledì'
        WHEN 4 THEN 'Giovedì'
        WHEN 5 THEN 'Venerdì'
        WHEN 6 THEN 'Sabato'
    END as day_name,
    ea.start_time,
    ea.end_time,
    ea.break_start_time,
    ea.break_end_time,
    ea.is_available,
    ea.notes,
    ea.created_at,
    ea.updated_at
FROM employee_availability ea
JOIN employees e ON ea.employee_id = e.id
WHERE e.is_active = true
ORDER BY e.name, ea.day_of_week, ea.start_time;

-- Function to check if an employee is available at a specific date and time
CREATE OR REPLACE FUNCTION is_employee_available(
    p_employee_id UUID,
    p_date DATE,
    p_time TIME
) RETURNS BOOLEAN AS $$
DECLARE
    v_day_of_week INTEGER;
    v_special_schedule RECORD;
    v_regular_availability RECORD;
BEGIN
    -- Get day of week (0=Sunday, 1=Monday, etc.)
    v_day_of_week := EXTRACT(DOW FROM p_date);
    
    -- Check for special schedule first (overrides regular availability)
    SELECT * INTO v_special_schedule
    FROM employee_special_schedules
    WHERE employee_id = p_employee_id AND date = p_date;
    
    IF FOUND THEN
        -- Special schedule exists
        IF NOT v_special_schedule.is_available THEN
            RETURN FALSE;
        END IF;
        
        -- Check if time is within special schedule hours
        IF p_time < v_special_schedule.start_time OR p_time >= v_special_schedule.end_time THEN
            RETURN FALSE;
        END IF;
        
        -- Check if time is during break
        IF v_special_schedule.break_start_time IS NOT NULL AND 
           p_time >= v_special_schedule.break_start_time AND 
           p_time < v_special_schedule.break_end_time THEN
            RETURN FALSE;
        END IF;
        
        RETURN TRUE;
    END IF;
    
    -- No special schedule, check regular availability
    SELECT * INTO v_regular_availability
    FROM employee_availability
    WHERE employee_id = p_employee_id 
      AND day_of_week = v_day_of_week
      AND is_available = true
      AND p_time >= start_time 
      AND p_time < end_time;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Check if time is during break
    IF v_regular_availability.break_start_time IS NOT NULL AND 
       p_time >= v_regular_availability.break_start_time AND 
       p_time < v_regular_availability.break_end_time THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to get available employees for a specific date and time
CREATE OR REPLACE FUNCTION get_available_employees(
    p_date DATE,
    p_time TIME
) RETURNS TABLE(
    employee_id UUID,
    employee_name VARCHAR(100),
    role VARCHAR(100),
    department VARCHAR(100),
    specializations TEXT[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e.id,
        e.name,
        e.role,
        e.department,
        e.specializations
    FROM employees e
    WHERE e.is_active = true 
      AND e.name != 'Qualsiasi'
      AND is_employee_available(e.id, p_date, p_time)
    ORDER BY e.name;
END;
$$ LANGUAGE plpgsql;

-- Grant access to views and functions
GRANT SELECT ON employee_availability_view TO anon, authenticated;
GRANT EXECUTE ON FUNCTION is_employee_available(UUID, DATE, TIME) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_available_employees(DATE, TIME) TO anon, authenticated;
