'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { services, servicePrestazioni, isWeekday, getMinDate, isValidPhone, isValidEmail } from '../lib/utils';
import {
  trackFormFieldInteraction,
  trackFormSubmission,
  trackBookingFunnel,
  trackAvailabilityCheck,
  trackError
} from '../lib/analytics';

export default function AppointmentForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [availabilityData, setAvailabilityData] = useState(null);
  const [isLoadingAvailability, setIsLoadingAvailability] = useState(false);
  const [selectedDate, setSelectedDate] = useState('');
  const [availabilityError, setAvailabilityError] = useState('');
  const [selectedService, setSelectedService] = useState('');
  const [availablePrestazioni, setAvailablePrestazioni] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(false);

  // Track form view on component mount
  useEffect(() => {
    trackBookingFunnel('form_viewed');
  }, []);

  // Fetch employees from API
  const fetchEmployees = async () => {
    try {
      setIsLoadingEmployees(true);
      const response = await fetch('/api/employees');
      const result = await response.json();

      if (response.ok && result.success) {
        setEmployees(result.data || []);
      } else {
        console.error('Error fetching employees:', result.message);
        // Fallback to default employees if API fails
        setEmployees([
          { id: 'qualsiasi', name: 'Qualsiasi' },
          { id: 'antonello', name: 'Antonello' },
          { id: 'federica', name: 'Federica' },
          { id: 'giuseppe', name: 'Giuseppe' },
          { id: 'silvia', name: 'Silvia' },
          { id: 'tania', name: 'Tania' }
        ]);
      }
    } catch (error) {
      console.error('Error fetching employees:', error);
      // Fallback to default employees if API fails
      setEmployees([
        { id: 'qualsiasi', name: 'Qualsiasi' },
        { id: 'antonello', name: 'Antonello' },
        { id: 'federica', name: 'Federica' },
        { id: 'giuseppe', name: 'Giuseppe' },
        { id: 'silvia', name: 'Silvia' },
        { id: 'tania', name: 'Tania' }
      ]);
    } finally {
      setIsLoadingEmployees(false);
    }
  };

  // Load employees on component mount
  useEffect(() => {
    fetchEmployees();
  }, []);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm();

  // Watch for date changes
  const watchedDate = watch('dataAppuntamento');

  // Watch for service changes
  const watchedService = watch('servizio');

  // Handle service changes
  useEffect(() => {
    if (watchedService && watchedService !== selectedService) {
      setSelectedService(watchedService);
      setAvailablePrestazioni(servicePrestazioni[watchedService] || []);
      // Clear prestazione selection when service changes
      setValue('prestazione', '');
      // Track service selection
      trackFormFieldInteraction('servizio', 'select', watchedService);
    }
  }, [watchedService, selectedService, setValue]);

  // Fetch availability when date changes
  useEffect(() => {
    if (watchedDate && watchedDate !== selectedDate) {
      setSelectedDate(watchedDate);
      fetchAvailability(watchedDate);
      // Track date selection
      trackBookingFunnel('date_selected', { selected_date: watchedDate });
    }
  }, [watchedDate, selectedDate]);

  // Function to fetch availability for a specific date
  const fetchAvailability = async (date) => {
    if (!date) return;

    setIsLoadingAvailability(true);
    setAvailabilityError('');
    setAvailabilityData(null);

    try {
      const response = await fetch(`/api/availability?date=${date}`);
      const result = await response.json();

      if (result.success) {
        setAvailabilityData(result);

        // Track availability check
        trackAvailabilityCheck(
          date,
          result.summary.available,
          result.summary.total
        );

        // Clear the time selection if the currently selected time is no longer available
        const currentTime = watch('orario');
        if (currentTime && !result.availableSlots.includes(currentTime)) {
          setValue('orario', '');
        }
      } else {
        setAvailabilityError(result.message || 'Errore nel controllo disponibilità');
        setAvailabilityData(null);
        // Track availability error
        trackError(`Availability check failed: ${result.message}`, false, 'fetchAvailability');
      }
    } catch (error) {
      setAvailabilityError('Errore di connessione. Riprova più tardi.');
      setAvailabilityData(null);
      // Track connection error
      trackError(`Availability fetch error: ${error.message}`, false, 'fetchAvailability');
    } finally {
      setIsLoadingAvailability(false);
    }
  };



  const onSubmit = async (data) => {
    setIsSubmitting(true);
    setSubmitMessage('');

    // Track form submission start
    trackFormSubmission('started', data);

    // Final availability check before submission
    if (availabilityData && !availabilityData.availableSlots.includes(data.orario)) {
      setSubmitMessage('L\'orario selezionato non è più disponibile. Seleziona un altro orario.');
      setIsSubmitting(false);
      trackFormSubmission('error', data, 'Time slot no longer available');
      return;
    }

    try {
      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Conditional success message based on whether email was provided
        const hasEmail = data.email && data.email.trim() !== '';
        const successMessage = hasEmail
          ? 'Appuntamento confermato! Riceverai una email di conferma.'
          : 'Appuntamento confermato! Ti aspettiamo alla data e ora concordate.';

        setSubmitMessage(successMessage);
        reset();
        setSelectedDate('');
        setAvailabilityData(null);
        setSelectedService('');
        setAvailablePrestazioni([]);
        // Track successful submission
        trackFormSubmission('success', data);
        trackBookingFunnel('form_completed', {
          service: data.servizio,
          prestazione: data.prestazione,
          date: data.dataAppuntamento,
          time: data.orario
        });
      } else {
        if (result.code === 'TIME_SLOT_UNAVAILABLE') {
          setSubmitMessage('L\'orario selezionato non è più disponibile. Ricarica la pagina e scegli un altro orario.');
          // Refresh availability for the selected date
          if (data.dataAppuntamento) {
            fetchAvailability(data.dataAppuntamento);
          }
          trackFormSubmission('error', data, 'Time slot unavailable during submission');
        } else {
          setSubmitMessage(result.message || 'Errore nell\'invio. Riprova più tardi.');
          trackFormSubmission('error', data, result.message || 'Unknown submission error');
        }
      }
    } catch (error) {
      setSubmitMessage('Errore di connessione. Riprova più tardi.');
      trackFormSubmission('error', data, `Connection error: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Nome */}
      <div>
        <label htmlFor="nome" className="form-label">
          Nome *
        </label>
        <input
          type="text"
          id="nome"
          className="form-input"
          {...register('nome', { 
            required: 'Il nome è obbligatorio',
            minLength: { value: 2, message: 'Il nome deve avere almeno 2 caratteri' }
          })}
        />
        {errors.nome && (
          <p className="error-message">{errors.nome.message}</p>
        )}
      </div>

      {/* Cognome */}
      <div>
        <label htmlFor="cognome" className="form-label">
          Cognome *
        </label>
        <input
          type="text"
          id="cognome"
          className="form-input"
          {...register('cognome', { 
            required: 'Il cognome è obbligatorio',
            minLength: { value: 2, message: 'Il cognome deve avere almeno 2 caratteri' }
          })}
        />
        {errors.cognome && (
          <p className="error-message">{errors.cognome.message}</p>
        )}
      </div>

      {/* Numero Telefono */}
      <div>
        <label htmlFor="telefono" className="form-label">
          Numero Telefono (opzionale)
        </label>
        <input
          type="tel"
          id="telefono"
          className="form-input"
          placeholder="Inserisci il tuo numero di telefono"
          {...register('telefono', {
            validate: {
              isValid: (value) =>
                !value || value.trim() === '' || isValidPhone(value) || 'Inserisci un numero di telefono valido'
            }
          })}
        />
        {errors.telefono && (
          <p className="error-message">{errors.telefono.message}</p>
        )}
      </div>

      {/* Email */}
      <div>
        <label htmlFor="email" className="form-label">
          Email (opzionale)
        </label>
        <input
          type="email"
          id="email"
          className="form-input"
          placeholder="Inserisci la tua email"
          {...register('email', {
            validate: {
              isValid: (value) =>
                !value || value.trim() === '' || isValidEmail(value) || 'Inserisci un indirizzo email valido'
            }
          })}
        />
        {errors.email && (
          <p className="error-message">{errors.email.message}</p>
        )}
        <p className="text-sm text-[var(--secondary-text)] mt-1">
          💡 Se non inserisci l'email, non riceverai la conferma dell'appuntamento via email
        </p>
      </div>

      {/* Servizio */}
      <div>
        <label htmlFor="servizio" className="form-label">
          Servizio *
        </label>
        <select
          id="servizio"
          className="form-input"
          {...register('servizio', { required: 'Seleziona un servizio' })}
        >
          <option value="">Seleziona un servizio</option>
          {services.map((service) => (
            <option key={service} value={service}>
              {service}
            </option>
          ))}
        </select>
        {errors.servizio && (
          <p className="error-message">{errors.servizio.message}</p>
        )}
      </div>

      {/* Prestazione - Only show if service is selected and has prestazioni */}
      {selectedService && availablePrestazioni.length > 0 && (
        <div>
          <label htmlFor="prestazione" className="form-label">
            Prestazione *
          </label>
          <select
            id="prestazione"
            className="form-input"
            {...register('prestazione', {
              required: selectedService && availablePrestazioni.length > 0 ? 'Seleziona una prestazione' : false
            })}
            onChange={(e) => {
              if (e.target.value) {
                trackFormFieldInteraction('prestazione', 'select', e.target.value);
              }
            }}
          >
            <option value="">Seleziona una prestazione</option>
            {availablePrestazioni.map((prestazione) => (
              <option key={prestazione} value={prestazione}>
                {prestazione}
              </option>
            ))}
          </select>
          {errors.prestazione && (
            <p className="error-message">{errors.prestazione.message}</p>
          )}
        </div>
      )}

      {/* Operatore */}
      <div>
        <label htmlFor="operatore" className="form-label">
          Operatore
        </label>
        <select
          id="operatore"
          className="form-input"
          {...register('operatore')}
          defaultValue="Qualsiasi"
          disabled={isLoadingEmployees}
        >
          {isLoadingEmployees ? (
            <option value="">Caricamento operatori...</option>
          ) : (
            employees.map((employee) => (
              <option key={employee.id} value={employee.name}>
                {employee.name}
              </option>
            ))
          )}
        </select>
        {errors.operatore && (
          <p className="error-message">{errors.operatore.message}</p>
        )}
      </div>

      {/* Note Aggiuntive */}
      <div>
        <label htmlFor="noteAggiuntive" className="form-label">
          Note Aggiuntive (opzionale)
        </label>
        <textarea
          id="noteAggiuntive"
          className="form-input"
          rows="3"
          placeholder="Inserisci eventuali note aggiuntive..."
          {...register('noteAggiuntive')}
        />
        {errors.noteAggiuntive && (
          <p className="error-message">{errors.noteAggiuntive.message}</p>
        )}
      </div>

      {/* Data Appuntamento */}
      <div>
        <label htmlFor="dataAppuntamento" className="form-label">
          Data Appuntamento *
        </label>
        <input
          type="date"
          id="dataAppuntamento"
          className="form-input"
          min={getMinDate()}
          {...register('dataAppuntamento', {
            required: 'La data dell\'appuntamento è obbligatoria',
            validate: {
              isWeekday: (value) =>
                isWeekday(value) || 'Seleziona un giorno dal lunedì al venerdì'
            }
          })}
        />

        {/* Date selection helper */}
        <div className="mt-2 text-sm text-[var(--secondary-text)]">
          <p>💡 Gli appuntamenti sono disponibili dal lunedì al venerdì</p>
          {selectedDate && !isLoadingAvailability && availabilityData && (
            <p className="mt-1">
              {availabilityData.success ? (
                availabilityData.summary.available > 0 ? (
                  <span className="text-green-600">
                    ✅ {availabilityData.summary.available} orari disponibili per {selectedDate}
                  </span>
                ) : (
                  <span className="text-red-600">
                    ❌ Nessun orario disponibile per {selectedDate}
                  </span>
                )
              ) : (
                <span className="text-red-600">
                  ❌ {availabilityData.message}
                </span>
              )}
            </p>
          )}
        </div>

        {errors.dataAppuntamento && (
          <p className="error-message">{errors.dataAppuntamento.message}</p>
        )}
      </div>

      {/* Orario */}
      <div>
        <label htmlFor="orario" className="form-label">
          Orario *
        </label>

        {/* Show loading state while checking availability */}
        {isLoadingAvailability && selectedDate && (
          <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              <span className="text-sm text-blue-700">Controllo disponibilità...</span>
            </div>
          </div>
        )}

        {/* Show availability error */}
        {availabilityError && (
          <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-700">{availabilityError}</p>
          </div>
        )}

        {/* Show availability summary */}
        {availabilityData && availabilityData.success && (
          <div className="mb-3 p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-700">
              <strong>{availabilityData.summary.available}</strong> orari disponibili su {availabilityData.summary.total}
              {availabilityData.summary.available === 0 && (
                <span className="block mt-1 text-red-600">
                  Nessun orario disponibile per questa data. Scegli un&apos;altra data.
                </span>
              )}
            </p>
          </div>
        )}

        <select
          id="orario"
          className="form-input"
          disabled={!selectedDate || isLoadingAvailability || (availabilityData && availabilityData.summary.available === 0)}
          {...register('orario', {
            required: 'Seleziona un orario',
            validate: {
              isAvailable: (value) => {
                if (!value) return true; // Let required validation handle empty values
                if (!availabilityData) return 'Seleziona prima una data';
                return availabilityData.availableSlots.includes(value) || 'Questo orario non è più disponibile';
              }
            }
          })}
          onChange={(e) => {
            if (e.target.value) {
              trackFormFieldInteraction('orario', 'select', e.target.value);
              trackBookingFunnel('time_selected', {
                selected_time: e.target.value,
                selected_date: selectedDate
              });
            }
          }}
        >
          <option value="">
            {!selectedDate
              ? 'Seleziona prima una data'
              : isLoadingAvailability
                ? 'Controllo disponibilità...'
                : availabilityData && availabilityData.summary.available === 0
                  ? 'Nessun orario disponibile'
                  : 'Seleziona un orario'
            }
          </option>

          {availabilityData && availabilityData.timeGroups && (
            <>
              {/* Morning slots */}
              {availabilityData.timeGroups.morning.some(slot => slot.available) && (
                <optgroup label="Mattina">
                  {availabilityData.timeGroups.morning
                    .filter(slot => slot.available)
                    .map((slot) => (
                      <option key={slot.time} value={slot.time}>
                        {slot.time}
                      </option>
                    ))}
                </optgroup>
              )}

              {/* Afternoon slots */}
              {availabilityData.timeGroups.afternoon.some(slot => slot.available) && (
                <optgroup label="Pomeriggio">
                  {availabilityData.timeGroups.afternoon
                    .filter(slot => slot.available)
                    .map((slot) => (
                      <option key={slot.time} value={slot.time}>
                        {slot.time}
                      </option>
                    ))}
                </optgroup>
              )}
            </>
          )}
        </select>

        {/* Show booked slots information */}
        {availabilityData && availabilityData.bookedSlots.length > 0 && (
          <div className="mt-2 p-2 bg-gray-50 rounded text-sm text-gray-600">
            <strong>Orari non disponibili:</strong> {availabilityData.bookedSlots.join(', ')}
          </div>
        )}

        {errors.orario && (
          <p className="error-message">{errors.orario.message}</p>
        )}
      </div>

      {/* Disclaimer */}
      <div className="text-sm text-[var(--secondary-text)] italic bg-gray-50 p-4 rounded-lg">
        *Ti ricordiamo che l&apos;orario potrebbe subire lievi variazioni: se al tuo arrivo un altro cliente è già in fase di servizio, verrai chiamato subito dopo.
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={isSubmitting || isLoadingAvailability || (availabilityData && availabilityData.summary.available === 0)}
        className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isSubmitting
          ? 'Invio in corso...'
          : isLoadingAvailability
            ? 'Controllo disponibilità...'
            : availabilityData && availabilityData.summary.available === 0
              ? 'Nessun orario disponibile'
              : 'CONFERMA APPUNTAMENTO'
        }
      </button>

      {/* Submit Message */}
      {submitMessage && (
        <div className={`text-center p-4 rounded-lg ${
          submitMessage.includes('confermato')
            ? 'bg-green-100 text-green-800 border border-green-200'
            : submitMessage.includes('disponibile') || submitMessage.includes('conflitto')
              ? 'bg-yellow-100 text-yellow-800 border border-yellow-200'
              : 'bg-red-100 text-red-800 border border-red-200'
        }`}>
          <div className="flex items-center justify-center">
            {submitMessage.includes('confermato') && (
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            )}
            {(submitMessage.includes('disponibile') || submitMessage.includes('conflitto')) && (
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            )}
            {!submitMessage.includes('confermato') && !submitMessage.includes('disponibile') && !submitMessage.includes('conflitto') && (
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            )}
            <span>{submitMessage}</span>
          </div>

          {/* Show alternative suggestions for conflicts */}
          {submitMessage.includes('disponibile') && availabilityData && availabilityData.availableSlots.length > 0 && (
            <div className="mt-3 text-sm">
              <p className="font-medium">Orari alternativi disponibili:</p>
              <div className="mt-1 flex flex-wrap gap-2">
                {availabilityData.availableSlots.slice(0, 6).map(time => (
                  <button
                    key={time}
                    type="button"
                    onClick={() => setValue('orario', time)}
                    className="px-2 py-1 bg-white border border-yellow-300 rounded text-yellow-800 hover:bg-yellow-50 transition-colors"
                  >
                    {time}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </form>
  );
}
