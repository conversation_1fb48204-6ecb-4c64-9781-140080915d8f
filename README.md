# CAF Appointment Booking System

A responsive web application for Centro di Assistenza Fiscale (CAF) that allows clients to book appointments online. Built with Next.js and Tailwind CSS.

## Features

- **Mobile-first responsive design** optimized for smartphones
- **Appointment booking form** with validation
- **Email confirmation system** for both clients and CAF office
- **Date/time restrictions** (Monday-Friday, specific time slots)
- **Multi-service support** (Patronato, Legal, Immigration)
- **Italian language interface**
- **Accessibility compliant** with semantic HTML

## Tech Stack

- **Next.js 15.3.3** - React framework
- **Tailwind CSS v4** - Styling
- **React Hook Form** - Form management and validation
- **Nodemailer** - Email sending functionality

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Email account for sending notifications (Gmail recommended)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd caf-form
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Configure your email settings in `.env.local`:
```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
CAF_EMAIL=<EMAIL>
```

**Note for Gmail users:** You need to use an App Password instead of your regular password:
1. Go to Google Account settings
2. Security > 2-Step Verification
3. App passwords > Generate new password
4. Use the generated password in `EMAIL_PASS`

5. Run the development server:
```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Form Fields

The appointment form includes:

1. **Nome** (First Name) - Required text input
2. **Cognome** (Last Name) - Required text input
3. **Numero Telefono** (Phone) - Required tel input with validation
4. **Email** - Required email input with validation
5. **Servizio** (Service) - Dropdown with options:
   - Servizi Patronato
   - Avvocato
   - Sportello Immigrazione
6. **Data Appuntamento** (Date) - Date picker (Monday-Friday only)
7. **Orario** (Time) - Time slots:
   - Morning: 09:00 - 13:00 (hourly)
   - Afternoon: 15:00 - 18:00 (hourly)

## Color Palette

- **Primary Red**: #B42C2C (titles, accents)
- **Secondary Blue**: #252B59 (header, footer)
- **Light Background**: #F7F7F5 (main background)
- **Border Gray**: #D1D1D1 (input borders)
- **Primary Text**: #1F1F1F (main text)
- **Secondary Text**: #555555 (supporting text)
- **Hover Accent**: #6B1F1F (button hover)

## Email Functionality

When a form is submitted:
1. **User receives** a confirmation email with appointment details
2. **CAF office receives** a notification email with client information
3. Both emails are formatted with HTML templates including branding

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy

### Other Platforms

Ensure your hosting platform supports:
- Node.js 18+
- Environment variables
- API routes

## Customization

### Logo
Replace the logo in `public/img/logo/logo.png` with your CAF logo.

### Email Templates
Modify email templates in `app/api/send-email/route.js`.

### Styling
Update colors and styles in `app/globals.css` and component files.

### Services
Modify available services in `components/AppointmentForm.js`.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For technical support or questions about the CAF appointment system, please contact the development team.
