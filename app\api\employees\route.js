import { NextResponse } from 'next/server';
import { getEmployees, getEmployeesBySpecialization } from '../../../lib/supabaseEmployeeUtils.js';

/**
 * GET /api/employees
 * Public endpoint to get active employees for appointment booking
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const specialization = searchParams.get('specialization');

    let employees;

    if (specialization) {
      // Get employees by specialization
      employees = await getEmployeesBySpecialization(specialization);
    } else {
      // Get all active employees
      employees = await getEmployees(true); // activeOnly = true
    }

    // Return simplified employee data for public use
    const publicEmployeeData = employees.map(employee => ({
      id: employee.id,
      name: employee.name,
      role: employee.role,
      department: employee.department,
      specializations: employee.specializations
    }));

    return NextResponse.json({
      success: true,
      data: publicEmployeeData
    });

  } catch (error) {
    console.error('Error in GET /api/employees:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Errore nel recupero dei dipendenti',
        error: error.message 
      },
      { status: 500 }
    );
  }
}
