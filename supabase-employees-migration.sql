-- CAF Employee Management System - Database Migration
-- Run this in your Supabase SQL Editor to add employee management functionality

-- Create employees table
CREATE TABLE IF NOT EXISTS employees (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE,
    role VARCHAR(100),
    department VARCHAR(100),
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    specializations TEXT[], -- Array of specializations/services they can handle
    working_hours JSONB DEFAULT '{"monday": {"start": "09:00", "end": "18:00"}, "tuesday": {"start": "09:00", "end": "18:00"}, "wednesday": {"start": "09:00", "end": "18:00"}, "thursday": {"start": "09:00", "end": "18:00"}, "friday": {"start": "09:00", "end": "18:00"}, "saturday": null, "sunday": null}',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_employees_name ON employees(name);
CREATE INDEX IF NOT EXISTS idx_employees_email ON employees(email);
CREATE INDEX IF NOT EXISTS idx_employees_active ON employees(is_active);
CREATE INDEX IF NOT EXISTS idx_employees_department ON employees(department);

-- Enable Row Level Security on employees table
ALTER TABLE employees ENABLE ROW LEVEL SECURITY;

-- Create policies for employees table
-- Allow anon users to read active employees (for appointment booking)
CREATE POLICY "Allow anon to read active employees" ON employees
    FOR SELECT TO anon
    USING (is_active = true);

-- Allow anon users full access for admin operations (API routes handle auth)
CREATE POLICY "Allow anon full access to employees" ON employees
    FOR ALL TO anon
    USING (true)
    WITH CHECK (true);

-- Add employee_id column to appointments table to create relationship
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS employee_id UUID REFERENCES employees(id);

-- Create index for the foreign key
CREATE INDEX IF NOT EXISTS idx_appointments_employee_id ON appointments(employee_id);

-- Update the TABLES constant in lib/supabase.js by adding employees table
-- This will be done in the application code

-- Insert default employees (migrating from hardcoded values)
INSERT INTO employees (name, email, role, department, is_active, specializations) VALUES
    ('Qualsiasi', NULL, 'Generale', 'Tutti i Servizi', true, ARRAY['Servizi CAF', 'Patronato', 'Avvocato', 'Medico', 'Prestiti', 'Immigrazione', 'Altri Servizi']),
    ('Antonello', '<EMAIL>', 'Operatore CAF', 'Servizi CAF', true, ARRAY['Servizi CAF', 'Patronato']),
    ('Federica', '<EMAIL>', 'Consulente Legale', 'Servizi Legali', true, ARRAY['Avvocato', 'Immigrazione']),
    ('Giuseppe', '<EMAIL>', 'Operatore Patronato', 'Patronato', true, ARRAY['Patronato', 'Prestiti']),
    ('Silvia', '<EMAIL>', 'Consulente Medico', 'Servizi Medici', true, ARRAY['Medico', 'Altri Servizi']),
    ('Tania', '<EMAIL>', 'Operatore CAF Senior', 'Servizi CAF', true, ARRAY['Servizi CAF', 'Patronato', 'Immigrazione'])
ON CONFLICT (email) DO NOTHING;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_employees_updated_at 
    BEFORE UPDATE ON employees 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT ALL ON employees TO anon, authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Create view for active employees (useful for API queries)
CREATE OR REPLACE VIEW active_employees AS
SELECT 
    id,
    name,
    email,
    role,
    department,
    phone,
    specializations,
    working_hours,
    notes,
    created_at,
    updated_at
FROM employees 
WHERE is_active = true
ORDER BY name;

-- Grant access to the view
GRANT SELECT ON active_employees TO anon, authenticated;

-- ========================================
-- EMPLOYEE AVAILABILITY SYSTEM ENHANCEMENT
-- ========================================

-- Create employee_availability table for detailed scheduling
CREATE TABLE IF NOT EXISTS employee_availability (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0=Sunday, 1=Monday, ..., 6=Saturday
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT true,
    break_start_time TIME, -- Optional break time
    break_end_time TIME,   -- Optional break end time
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure end_time is after start_time
    CONSTRAINT valid_time_range CHECK (end_time > start_time),
    -- Ensure break times are valid if specified
    CONSTRAINT valid_break_times CHECK (
        (break_start_time IS NULL AND break_end_time IS NULL) OR
        (break_start_time IS NOT NULL AND break_end_time IS NOT NULL AND
         break_end_time > break_start_time AND
         break_start_time >= start_time AND break_end_time <= end_time)
    ),
    -- Prevent overlapping availability slots for same employee/day
    UNIQUE(employee_id, day_of_week, start_time, end_time)
);

-- Create employee_special_schedules table for exceptions (holidays, special hours, etc.)
CREATE TABLE IF NOT EXISTS employee_special_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    is_available BOOLEAN DEFAULT false, -- Default to unavailable for special dates
    start_time TIME,
    end_time TIME,
    break_start_time TIME,
    break_end_time TIME,
    reason VARCHAR(255), -- Holiday, sick leave, special hours, etc.
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure end_time is after start_time when available
    CONSTRAINT valid_special_time_range CHECK (
        NOT is_available OR (is_available AND end_time > start_time)
    ),
    -- Ensure break times are valid if specified
    CONSTRAINT valid_special_break_times CHECK (
        (break_start_time IS NULL AND break_end_time IS NULL) OR
        (break_start_time IS NOT NULL AND break_end_time IS NOT NULL AND
         break_end_time > break_start_time AND
         break_start_time >= start_time AND break_end_time <= end_time)
    ),
    -- One record per employee per date
    UNIQUE(employee_id, date)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_employee_availability_employee_day ON employee_availability(employee_id, day_of_week);
CREATE INDEX IF NOT EXISTS idx_employee_availability_day_time ON employee_availability(day_of_week, start_time, end_time);
CREATE INDEX IF NOT EXISTS idx_employee_special_schedules_employee_date ON employee_special_schedules(employee_id, date);
CREATE INDEX IF NOT EXISTS idx_employee_special_schedules_date ON employee_special_schedules(date);

-- Enable Row Level Security
ALTER TABLE employee_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_special_schedules ENABLE ROW LEVEL SECURITY;

-- Create policies for employee_availability table
CREATE POLICY "Allow anon to read employee availability" ON employee_availability
    FOR SELECT TO anon
    USING (true);

CREATE POLICY "Allow anon full access to employee availability" ON employee_availability
    FOR ALL TO anon
    USING (true)
    WITH CHECK (true);

-- Create policies for employee_special_schedules table
CREATE POLICY "Allow anon to read employee special schedules" ON employee_special_schedules
    FOR SELECT TO anon
    USING (true);

CREATE POLICY "Allow anon full access to employee special schedules" ON employee_special_schedules
    FOR ALL TO anon
    USING (true)
    WITH CHECK (true);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_employee_availability_updated_at
    BEFORE UPDATE ON employee_availability
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employee_special_schedules_updated_at
    BEFORE UPDATE ON employee_special_schedules
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT ALL ON employee_availability TO anon, authenticated;
GRANT ALL ON employee_special_schedules TO anon, authenticated;

-- Insert default availability for existing employees (Monday-Friday, 9:00-18:00 with lunch break)
INSERT INTO employee_availability (employee_id, day_of_week, start_time, end_time, break_start_time, break_end_time, is_available)
SELECT
    e.id,
    generate_series(1, 5) as day_of_week, -- Monday to Friday
    '09:00'::TIME as start_time,
    '18:00'::TIME as end_time,
    '12:00'::TIME as break_start_time,
    '15:00'::TIME as break_end_time,
    true as is_available
FROM employees e
WHERE e.name != 'Qualsiasi' -- Skip the "any employee" option
ON CONFLICT (employee_id, day_of_week, start_time, end_time) DO NOTHING;

-- Create view for employee availability with day names
CREATE OR REPLACE VIEW employee_availability_view AS
SELECT
    ea.id,
    ea.employee_id,
    e.name as employee_name,
    ea.day_of_week,
    CASE ea.day_of_week
        WHEN 0 THEN 'Domenica'
        WHEN 1 THEN 'Lunedì'
        WHEN 2 THEN 'Martedì'
        WHEN 3 THEN 'Mercoledì'
        WHEN 4 THEN 'Giovedì'
        WHEN 5 THEN 'Venerdì'
        WHEN 6 THEN 'Sabato'
    END as day_name,
    ea.start_time,
    ea.end_time,
    ea.break_start_time,
    ea.break_end_time,
    ea.is_available,
    ea.notes,
    ea.created_at,
    ea.updated_at
FROM employee_availability ea
JOIN employees e ON ea.employee_id = e.id
WHERE e.is_active = true
ORDER BY e.name, ea.day_of_week, ea.start_time;

-- Create view for checking employee availability on specific dates
CREATE OR REPLACE VIEW employee_date_availability AS
SELECT
    e.id as employee_id,
    e.name as employee_name,
    CURRENT_DATE + generate_series(0, 30) as date, -- Next 30 days
    EXTRACT(DOW FROM CURRENT_DATE + generate_series(0, 30)) as day_of_week
FROM employees e
WHERE e.is_active = true AND e.name != 'Qualsiasi';

-- Grant access to views
GRANT SELECT ON employee_availability_view TO anon, authenticated;
GRANT SELECT ON employee_date_availability TO anon, authenticated;
