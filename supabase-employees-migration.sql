-- CAF Employee Management System - Database Migration
-- Run this in your Supabase SQL Editor to add employee management functionality

-- Create employees table
CREATE TABLE IF NOT EXISTS employees (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE,
    role VARCHAR(100),
    department VARCHAR(100),
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    specializations TEXT[], -- Array of specializations/services they can handle
    working_hours JSONB DEFAULT '{"monday": {"start": "09:00", "end": "18:00"}, "tuesday": {"start": "09:00", "end": "18:00"}, "wednesday": {"start": "09:00", "end": "18:00"}, "thursday": {"start": "09:00", "end": "18:00"}, "friday": {"start": "09:00", "end": "18:00"}, "saturday": null, "sunday": null}',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_employees_name ON employees(name);
CREATE INDEX IF NOT EXISTS idx_employees_email ON employees(email);
CREATE INDEX IF NOT EXISTS idx_employees_active ON employees(is_active);
CREATE INDEX IF NOT EXISTS idx_employees_department ON employees(department);

-- Enable Row Level Security on employees table
ALTER TABLE employees ENABLE ROW LEVEL SECURITY;

-- Create policies for employees table
-- Allow anon users to read active employees (for appointment booking)
CREATE POLICY "Allow anon to read active employees" ON employees
    FOR SELECT TO anon
    USING (is_active = true);

-- Allow anon users full access for admin operations (API routes handle auth)
CREATE POLICY "Allow anon full access to employees" ON employees
    FOR ALL TO anon
    USING (true)
    WITH CHECK (true);

-- Add employee_id column to appointments table to create relationship
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS employee_id UUID REFERENCES employees(id);

-- Create index for the foreign key
CREATE INDEX IF NOT EXISTS idx_appointments_employee_id ON appointments(employee_id);

-- Update the TABLES constant in lib/supabase.js by adding employees table
-- This will be done in the application code

-- Insert default employees (migrating from hardcoded values)
INSERT INTO employees (name, email, role, department, is_active, specializations) VALUES
    ('Qualsiasi', NULL, 'Generale', 'Tutti i Servizi', true, ARRAY['Servizi CAF', 'Patronato', 'Avvocato', 'Medico', 'Prestiti', 'Immigrazione', 'Altri Servizi']),
    ('Antonello', '<EMAIL>', 'Operatore CAF', 'Servizi CAF', true, ARRAY['Servizi CAF', 'Patronato']),
    ('Federica', '<EMAIL>', 'Consulente Legale', 'Servizi Legali', true, ARRAY['Avvocato', 'Immigrazione']),
    ('Giuseppe', '<EMAIL>', 'Operatore Patronato', 'Patronato', true, ARRAY['Patronato', 'Prestiti']),
    ('Silvia', '<EMAIL>', 'Consulente Medico', 'Servizi Medici', true, ARRAY['Medico', 'Altri Servizi']),
    ('Tania', '<EMAIL>', 'Operatore CAF Senior', 'Servizi CAF', true, ARRAY['Servizi CAF', 'Patronato', 'Immigrazione'])
ON CONFLICT (email) DO NOTHING;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_employees_updated_at 
    BEFORE UPDATE ON employees 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT ALL ON employees TO anon, authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Create view for active employees (useful for API queries)
CREATE OR REPLACE VIEW active_employees AS
SELECT 
    id,
    name,
    email,
    role,
    department,
    phone,
    specializations,
    working_hours,
    notes,
    created_at,
    updated_at
FROM employees 
WHERE is_active = true
ORDER BY name;

-- Grant access to the view
GRANT SELECT ON active_employees TO anon, authenticated;
