/**
 * Debug <PERSON>ript for Appointment ID Issues
 * This script helps trace appointment ID handling from UI to database
 *
 * Usage: Add this to your browser console or run as a script
 */

// Browser Console Debugging Functions
window.debugAppointmentIds = {
  
  // Function to inspect appointment data in the UI
  inspectUIAppointments: function() {
    console.log('🔍 Inspecting appointment data in UI...');
    
    // Find all appointment rows in the table
    const appointmentRows = document.querySelectorAll('tr[data-appointment-id], tr:has(td:contains("ID:"))');
    
    if (appointmentRows.length === 0) {
      console.log('❌ No appointment rows found in UI');
      return;
    }
    
    console.log(`✅ Found ${appointmentRows.length} appointment rows`);
    
    appointmentRows.forEach((row, index) => {
      // Try to extract ID from the row
      const idCell = row.querySelector('td:nth-child(2)'); // Cliente column
      if (idCell) {
        const idText = idCell.textContent;
        const idMatch = idText.match(/ID:\s*(\d+)/);
        if (idMatch) {
          console.log(`Row ${index + 1}: ID = ${idMatch[1]} (type: ${typeof idMatch[1]})`);
          
          // Check if buttons exist and have proper event handlers
          const statusSelect = row.querySelector('select');
          const deleteButton = row.querySelector('button[title*="Elimina"]');
          const viewButton = row.querySelector('button[title*="Visualizza"]');
          
          console.log(`  - Status select: ${statusSelect ? 'Found' : 'Missing'}`);
          console.log(`  - Delete button: ${deleteButton ? 'Found' : 'Missing'}`);
          console.log(`  - View button: ${viewButton ? 'Found' : 'Missing'}`);
          
          if (statusSelect) {
            console.log(`  - Status select value: ${statusSelect.value}`);
          }
        }
      }
    });
  },
  
  // Function to monitor API calls
  monitorAPIRequests: function() {
    console.log('🔍 Setting up API request monitoring...');
    
    // Override fetch to monitor API calls
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
      const [url, options] = args;
      
      if (url.includes('/api/admin/appointments')) {
        console.log('🌐 API Request intercepted:');
        console.log('  URL:', url);
        console.log('  Method:', options?.method || 'GET');
        
        if (options?.body) {
          try {
            const body = JSON.parse(options.body);
            console.log('  Body:', body);
            if (body.appointmentId) {
              console.log(`  Appointment ID: ${body.appointmentId} (type: ${typeof body.appointmentId})`);
            }
          } catch (e) {
            console.log('  Body (raw):', options.body);
          }
        }
        
        // Extract ID from URL for DELETE requests
        if (url.includes('?id=')) {
          const urlParams = new URLSearchParams(url.split('?')[1]);
          const id = urlParams.get('id');
          console.log(`  URL ID parameter: ${id} (type: ${typeof id})`);
        }
      }
      
      return originalFetch.apply(this, args).then(response => {
        if (url.includes('/api/admin/appointments')) {
          console.log(`🌐 API Response: ${response.status} ${response.statusText}`);
          
          // Clone response to read body without consuming it
          const clonedResponse = response.clone();
          clonedResponse.json().then(data => {
            console.log('  Response data:', data);
          }).catch(() => {
            console.log('  Response: (non-JSON)');
          });
        }
        return response;
      });
    };
    
    console.log('✅ API monitoring active. Perform appointment operations to see requests.');
  },
  
  // Function to test ID extraction from UI elements
  testIDExtraction: function() {
    console.log('🔍 Testing ID extraction from UI elements...');
    
    // Find the first appointment row
    const firstRow = document.querySelector('tr:has(td:contains("ID:"))');
    if (!firstRow) {
      console.log('❌ No appointment rows found');
      return;
    }
    
    // Extract ID
    const idCell = firstRow.querySelector('td:nth-child(2)');
    const idText = idCell?.textContent;
    const idMatch = idText?.match(/ID:\s*(\d+)/);
    
    if (idMatch) {
      const extractedId = idMatch[1];
      console.log(`✅ Extracted ID: ${extractedId} (type: ${typeof extractedId})`);
      
      // Test different ID formats
      const numericId = parseInt(extractedId, 10);
      const stringId = String(extractedId);
      
      console.log(`  - As number: ${numericId} (type: ${typeof numericId})`);
      console.log(`  - As string: "${stringId}" (type: ${typeof stringId})`);
      
      // Test the status select element
      const statusSelect = firstRow.querySelector('select');
      if (statusSelect) {
        console.log(`  - Status select found, current value: ${statusSelect.value}`);
        
        // Add a temporary event listener to test
        const testHandler = (e) => {
          console.log('🎯 Status change event fired:');
          console.log(`    - ID: ${extractedId} (type: ${typeof extractedId})`);
          console.log(`    - New status: ${e.target.value}`);
          console.log(`    - Event target:`, e.target);
        };
        
        statusSelect.addEventListener('change', testHandler, { once: true });
        console.log('✅ Test event listener added to status select');
      }
      
      // Test the delete button
      const deleteButton = firstRow.querySelector('button[title*="Elimina"]');
      if (deleteButton) {
        console.log('  - Delete button found');
        
        // Add a temporary event listener to test
        const testDeleteHandler = (e) => {
          e.preventDefault();
          console.log('🎯 Delete button clicked:');
          console.log(`    - ID: ${extractedId} (type: ${typeof extractedId})`);
          console.log(`    - Event target:`, e.target);
        };
        
        deleteButton.addEventListener('click', testDeleteHandler, { once: true });
        console.log('✅ Test event listener added to delete button');
      }
    } else {
      console.log('❌ Could not extract ID from first row');
    }
  },
  
  // Function to simulate appointment operations
  simulateOperations: function() {
    console.log('🔍 Simulating appointment operations...');
    
    // Find the first appointment
    const firstRow = document.querySelector('tr:has(td:contains("ID:"))');
    if (!firstRow) {
      console.log('❌ No appointment rows found');
      return;
    }
    
    // Extract ID
    const idCell = firstRow.querySelector('td:nth-child(2)');
    const idText = idCell?.textContent;
    const idMatch = idText?.match(/ID:\s*(\d+)/);
    
    if (!idMatch) {
      console.log('❌ Could not extract ID');
      return;
    }
    
    const appointmentId = idMatch[1];
    console.log(`Testing with appointment ID: ${appointmentId}`);
    
    // Test status update
    console.log('\n📝 Testing status update...');
    const statusSelect = firstRow.querySelector('select');
    if (statusSelect) {
      const currentStatus = statusSelect.value;
      const newStatus = currentStatus === 'confirmed' ? 'completed' : 'confirmed';
      
      console.log(`Changing status from ${currentStatus} to ${newStatus}`);
      
      // Trigger the change event
      statusSelect.value = newStatus;
      statusSelect.dispatchEvent(new Event('change', { bubbles: true }));
    }
    
    // Test delete (with confirmation override)
    console.log('\n🗑️ Testing delete operation...');
    const deleteButton = firstRow.querySelector('button[title*="Elimina"]');
    if (deleteButton) {
      // Override confirm to prevent actual deletion
      const originalConfirm = window.confirm;
      window.confirm = () => {
        console.log('Confirm dialog would appear - simulating user clicking OK');
        window.confirm = originalConfirm; // Restore original
        return true;
      };
      
      deleteButton.click();
    }
  },
  
  // Function to check React component state
  checkReactState: function() {
    console.log('🔍 Checking React component state...');
    
    // Try to find React fiber nodes
    const appElement = document.querySelector('#__next') || document.querySelector('[data-reactroot]');
    if (appElement && appElement._reactInternalFiber) {
      console.log('✅ React fiber found');
      // This would require more complex React DevTools-like inspection
    } else {
      console.log('⚠️ React fiber not directly accessible');
    }
    
    // Check for any global state or debugging info
    if (window.React) {
      console.log('✅ React is available globally');
    }
    
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      console.log('✅ React DevTools hook available');
    }
  }
};

// Auto-run basic inspection
console.log('🚀 Appointment ID Debugging Tools Loaded');
console.log('Available functions:');
console.log('  - debugAppointmentIds.inspectUIAppointments()');
console.log('  - debugAppointmentIds.monitorAPIRequests()');
console.log('  - debugAppointmentIds.testIDExtraction()');
console.log('  - debugAppointmentIds.simulateOperations()');
console.log('  - debugAppointmentIds.checkReactState()');
console.log('');
console.log('💡 Quick start: Run debugAppointmentIds.monitorAPIRequests() first');
console.log('💡 Then try updating an appointment status or deleting an appointment');
console.log('💡 Watch the console for detailed logs of the entire process');

// Auto-run inspection if we're in a browser
if (typeof window !== 'undefined') {
  setTimeout(() => {
    console.log('\n🔍 Auto-running basic inspection...');
    window.debugAppointmentIds.inspectUIAppointments();
  }, 1000);
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
  module.exports = window.debugAppointmentIds;
}
