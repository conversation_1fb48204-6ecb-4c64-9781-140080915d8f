/**
 * Direct Database Test Script
 * Tests database operations directly to identify the exact issue
 *
 * Usage: node scripts/test-database-direct.js
 */

// Load environment variables first
import './load-env.js';

import { supabase, supabaseAdmin, TABLES } from '../lib/supabase.js';

console.log('🔍 Testing Database Operations Directly...\n');

async function testDatabaseClients() {
  console.log('1️⃣ Testing Database Clients...');
  
  try {
    // Test regular client
    console.log('Testing regular supabase client...');
    const { data: regularData, error: regularError } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('id, nome, cognome, status')
      .limit(5);
    
    if (regularError) {
      console.error('❌ Regular client error:', regularError);
    } else {
      console.log(`✅ Regular client: Found ${regularData.length} appointments`);
      if (regularData.length > 0) {
        console.log('Sample appointment (regular):', regularData[0]);
        console.log(`Sample ID type (regular): ${typeof regularData[0].id}`);
      }
    }
    
    // Test admin client
    console.log('\nTesting admin supabase client...');
    const { data: adminData, error: adminError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .select('id, nome, cognome, status')
      .limit(5);
    
    if (adminError) {
      console.error('❌ Admin client error:', adminError);
    } else {
      console.log(`✅ Admin client: Found ${adminData.length} appointments`);
      if (adminData.length > 0) {
        console.log('Sample appointment (admin):', adminData[0]);
        console.log(`Sample ID type (admin): ${typeof adminData[0].id}`);
      }
    }
    
    // Compare results
    if (regularData && adminData && regularData.length > 0 && adminData.length > 0) {
      console.log('\n🔍 Comparing client results...');
      console.log(`Regular client first ID: ${regularData[0].id} (${typeof regularData[0].id})`);
      console.log(`Admin client first ID: ${adminData[0].id} (${typeof adminData[0].id})`);
      
      if (regularData[0].id === adminData[0].id) {
        console.log('✅ Both clients return the same ID');
      } else {
        console.log('❌ Clients return different IDs!');
      }
    }
    
    return { regularData, adminData };
    
  } catch (error) {
    console.error('❌ Database client test failed:', error);
    return null;
  }
}

async function testSpecificAppointment(appointmentId) {
  console.log(`\n2️⃣ Testing Specific Appointment ID: ${appointmentId}...`);
  
  try {
    // Test with regular client
    console.log('Testing with regular client...');
    const { data: regularData, error: regularError } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .eq('id', appointmentId);
    
    console.log('Regular client result:', regularData);
    console.log('Regular client error:', regularError);
    
    // Test with admin client
    console.log('\nTesting with admin client...');
    const { data: adminData, error: adminError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .eq('id', appointmentId);
    
    console.log('Admin client result:', adminData);
    console.log('Admin client error:', adminError);
    
    // Test with string ID
    const stringId = String(appointmentId);
    console.log(`\nTesting with string ID: "${stringId}"...`);
    const { data: stringData, error: stringError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .eq('id', stringId);
    
    console.log('String ID result:', stringData);
    console.log('String ID error:', stringError);
    
    // Test with numeric ID
    const numericId = typeof appointmentId === 'string' ? parseInt(appointmentId, 10) : appointmentId;
    console.log(`\nTesting with numeric ID: ${numericId}...`);
    const { data: numericData, error: numericError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .eq('id', numericId);
    
    console.log('Numeric ID result:', numericData);
    console.log('Numeric ID error:', numericError);
    
  } catch (error) {
    console.error('❌ Specific appointment test failed:', error);
  }
}

async function testUpdateOperation(appointmentId) {
  console.log(`\n3️⃣ Testing Update Operation on ID: ${appointmentId}...`);
  
  try {
    // First, get the current status
    const { data: currentData, error: currentError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .select('id, status')
      .eq('id', appointmentId)
      .single();
    
    if (currentError || !currentData) {
      console.log('❌ Cannot find appointment for update test');
      return;
    }
    
    console.log(`Current appointment: ID ${currentData.id}, Status: ${currentData.status}`);
    
    // Try to update with a different status
    const newStatus = currentData.status === 'confirmed' ? 'completed' : 'confirmed';
    console.log(`Attempting to update status to: ${newStatus}`);
    
    const { data: updateData, error: updateError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .update({ status: newStatus })
      .eq('id', appointmentId)
      .select();
    
    console.log('Update result:', updateData);
    console.log('Update error:', updateError);
    
    if (updateData && updateData.length > 0) {
      console.log('✅ Update successful');
      
      // Revert the change
      const { data: revertData, error: revertError } = await supabaseAdmin
        .from(TABLES.APPOINTMENTS)
        .update({ status: currentData.status })
        .eq('id', appointmentId)
        .select();
      
      console.log('Revert result:', revertData);
      console.log('Revert error:', revertError);
    } else {
      console.log('❌ Update failed - no rows affected');
    }
    
  } catch (error) {
    console.error('❌ Update operation test failed:', error);
  }
}

async function testRLSPolicies() {
  console.log('\n4️⃣ Testing RLS Policies...');
  
  try {
    // Test with regular client (should be restricted by RLS)
    console.log('Testing regular client permissions...');
    const { data: readData, error: readError } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('id')
      .limit(1);
    
    console.log('Regular client read result:', readData);
    console.log('Regular client read error:', readError);
    
    // Test update with regular client (should fail)
    if (readData && readData.length > 0) {
      const testId = readData[0].id;
      console.log(`Testing regular client update on ID ${testId}...`);
      
      const { data: updateData, error: updateError } = await supabase
        .from(TABLES.APPOINTMENTS)
        .update({ status: 'confirmed' })
        .eq('id', testId)
        .select();
      
      console.log('Regular client update result:', updateData);
      console.log('Regular client update error:', updateError);
    }
    
    // Test with admin client (should bypass RLS)
    console.log('\nTesting admin client permissions...');
    const { data: adminReadData, error: adminReadError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .select('id')
      .limit(1);
    
    console.log('Admin client read result:', adminReadData);
    console.log('Admin client read error:', adminReadError);
    
  } catch (error) {
    console.error('❌ RLS policy test failed:', error);
  }
}

// Run all tests
async function runAllTests() {
  try {
    const clientResults = await testDatabaseClients();
    
    if (clientResults && clientResults.adminData && clientResults.adminData.length > 0) {
      const testId = clientResults.adminData[0].id;
      await testSpecificAppointment(testId);
      await testUpdateOperation(testId);
    }
    
    await testRLSPolicies();
    
    console.log('\n📋 Test Summary:');
    console.log('✅ Database client tests completed');
    console.log('✅ Specific appointment tests completed');
    console.log('✅ Update operation tests completed');
    console.log('✅ RLS policy tests completed');
    console.log('\n🔍 Check the output above for any errors or inconsistencies');
    
  } catch (error) {
    console.error('\n❌ Test suite failed:', error);
    process.exit(1);
  }
}

runAllTests().catch(console.error);
