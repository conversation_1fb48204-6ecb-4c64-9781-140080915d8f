import { createClient } from '@supabase/supabase-js';

const supabaseUrl = "https://ygfhovbxnpxufgwthbec.supabase.co";
const supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlnZmhvdmJ4bnB4dWZnd3RoYmVjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkyMDUzNDMsImV4cCI6MjA2NDc4MTM0M30.5LRe5nRSLUZl0ilGAGoIk915aUGgT4yoR8xn8pLqY9M";

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkAppointments() {
  try {
    console.log('🔍 Checking all appointments in database...');

    const { data, error } = await supabase
      .from('appointments')
      .select('id, nome, cognome')
      .order('id');

    if (error) {
      console.error('Error:', error);
      return;
    }

    console.log('Total appointments found:', data.length);
    console.log('First 10 appointments:');
    data.slice(0, 10).forEach(apt => {
      console.log(`  ID: ${apt.id} (${typeof apt.id}) - ${apt.nome} ${apt.cognome}`);
    });

    // Check specifically for ID 27
    const apt27 = data.find(apt => apt.id === 27 || apt.id === '27');
    console.log('\nAppointment with ID 27:', apt27);

    // Check if there are any appointments with ID around 27
    const nearby = data.filter(apt => apt.id >= 25 && apt.id <= 30);
    console.log('\nAppointments with IDs 25-30:', nearby);

    // Now test the readAppointments function from the utils
    console.log('\n🔍 Testing readAppointments function...');

    // Import the convertSupabaseToJsonFormat function
    const convertSupabaseToJsonFormat = (supabaseAppointment) => {
      return {
        id: supabaseAppointment.id.toString(),
        nome: supabaseAppointment.nome,
        cognome: supabaseAppointment.cognome,
        telefono: supabaseAppointment.telefono,
        email: supabaseAppointment.email,
        servizio: supabaseAppointment.servizio,
        prestazione: supabaseAppointment.prestazione,
        operatore: supabaseAppointment.operatore,
        noteAggiuntive: supabaseAppointment.note_aggiuntive,
        dataAppuntamento: supabaseAppointment.data_appuntamento,
        orario: supabaseAppointment.orario,
        status: supabaseAppointment.status,
        createdAt: supabaseAppointment.created_at,
        updatedAt: supabaseAppointment.updated_at
      };
    };

    // Simulate readAppointments function
    const { data: fullData, error: fullError } = await supabase
      .from('appointments')
      .select('*')
      .order('created_at', { ascending: false });

    if (fullError) {
      console.error('Error in readAppointments simulation:', fullError);
      return;
    }

    const convertedData = fullData.map(convertSupabaseToJsonFormat);
    console.log('Converted appointments:');
    convertedData.forEach(apt => {
      console.log(`  ID: ${apt.id} (${typeof apt.id}) - ${apt.nome} ${apt.cognome}`);
    });

    // Test the search logic used in deleteAppointment
    const appointmentId = '27'; // This is what comes from the frontend
    console.log(`\n🔍 Testing search logic for ID: ${appointmentId} (${typeof appointmentId})`);

    const targetAppointment = convertedData.find(apt =>
      apt.id == appointmentId || apt.id === appointmentId ||
      apt.id === String(appointmentId) || apt.id === parseInt(appointmentId, 10)
    );

    console.log('Found appointment:', targetAppointment);

    if (targetAppointment) {
      console.log('✅ Search logic works correctly');

      // Now test the actual delete operation
      console.log('\n🔍 Testing delete operation...');

      // Extract the actual database ID (convert back from string if needed)
      let dbId = targetAppointment.id;
      if (typeof dbId === 'string' && !isNaN(parseInt(dbId, 10))) {
        dbId = parseInt(dbId, 10);
      }

      console.log(`Using database ID for deletion: ${dbId} (type: ${typeof dbId})`);

      // Test delete with regular client (this might fail due to RLS)
      console.log('Testing delete with regular client...');
      const { data: deleteData, error: deleteError } = await supabase
        .from('appointments')
        .delete()
        .eq('id', dbId)
        .select();

      if (deleteError) {
        console.log('❌ Delete failed with regular client:', deleteError);
      } else {
        console.log('Delete result with regular client:', deleteData);
        if (!deleteData || deleteData.length === 0) {
          console.log('⚠️ No rows deleted with regular client (likely RLS issue)');

          // Check if RLS is enabled
          console.log('\n🔍 Checking RLS status...');
          const { data: rlsData, error: rlsError } = await supabase
            .from('pg_class')
            .select('relname, relrowsecurity')
            .eq('relname', 'appointments');

          if (rlsError) {
            console.log('Could not check RLS status:', rlsError);
          } else {
            console.log('RLS status:', rlsData);
          }

        } else {
          console.log('✅ Delete successful with regular client');
        }
      }

    } else {
      console.log('❌ Search logic failed');
      console.log('Available IDs:', convertedData.map(apt => `${apt.id} (${typeof apt.id})`));
    }

  } catch (error) {
    console.error('Error checking appointments:', error);
  }
}

checkAppointments();
