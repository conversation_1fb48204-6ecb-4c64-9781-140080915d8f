import { NextResponse } from 'next/server';
import { getAvailableEmployees, isEmployeeAvailable } from '../../../../lib/supabaseAvailabilityUtils.js';
import { getEmployees } from '../../../../lib/supabaseEmployeeUtils.js';
import { isWeekday } from '../../../../lib/utils.js';

/**
 * GET /api/employees/available
 * Get available employees for a specific date and time
 * Query parameters:
 * - date: Date in YYYY-MM-DD format (required)
 * - time: Time in HH:MM format (optional, if not provided returns employees available at any time)
 * - specialization: Filter by specialization (optional)
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    const time = searchParams.get('time');
    const specialization = searchParams.get('specialization');

    if (!date) {
      return NextResponse.json(
        { success: false, message: 'Data obbligatoria' },
        { status: 400 }
      );
    }

    // Validate that the date is a weekday
    if (!isWeekday(date)) {
      return NextResponse.json({
        success: false,
        message: 'Gli appuntamenti sono disponibili solo dal lunedì al venerdì',
        date,
        availableEmployees: []
      });
    }

    // Check if the date is in the past
    const selectedDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
      return NextResponse.json({
        success: false,
        message: 'Non è possibile prenotare appuntamenti per date passate',
        date,
        availableEmployees: []
      });
    }

    let availableEmployees = [];

    if (time) {
      // Get employees available at specific time
      availableEmployees = await getAvailableEmployees(date, time);
    } else {
      // Get all active employees and check their availability for the day
      const allEmployees = await getEmployees(true); // activeOnly = true
      
      // Filter out "Qualsiasi" employee for availability checking
      const realEmployees = allEmployees.filter(emp => emp.name !== 'Qualsiasi');
      
      // Check if each employee has any availability during the day
      const timeSlots = [
        '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00',
        '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00'
      ];

      for (const employee of realEmployees) {
        // Check if employee is available at any time slot
        let hasAvailability = false;
        for (const timeSlot of timeSlots) {
          if (await isEmployeeAvailable(employee.id, date, timeSlot)) {
            hasAvailability = true;
            break;
          }
        }

        if (hasAvailability) {
          availableEmployees.push({
            employee_id: employee.id,
            employee_name: employee.name,
            role: employee.role,
            department: employee.department,
            specializations: employee.specializations
          });
        }
      }
    }

    // Filter by specialization if provided
    if (specialization && availableEmployees.length > 0) {
      availableEmployees = availableEmployees.filter(emp => 
        emp.specializations && emp.specializations.includes(specialization)
      );
    }

    // Add "Qualsiasi" option if there are available employees
    if (availableEmployees.length > 0) {
      availableEmployees.unshift({
        employee_id: 'qualsiasi',
        employee_name: 'Qualsiasi',
        role: 'Generale',
        department: 'Tutti i Servizi',
        specializations: ['Servizi CAF', 'Patronato', 'Avvocato', 'Medico', 'Prestiti', 'Immigrazione', 'Altri Servizi']
      });
    }

    return NextResponse.json({
      success: true,
      date,
      time: time || null,
      specialization: specialization || null,
      availableEmployees,
      count: availableEmployees.length
    });

  } catch (error) {
    console.error('Error getting available employees:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Errore del server',
        error: error.message 
      },
      { status: 500 }
    );
  }
}
