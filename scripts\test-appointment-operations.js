/**
 * Simple Test Script for Appointment Operations
 * This script tests the core appointment operations to verify fixes
 *
 * Usage: node scripts/test-appointment-operations.js
 */

// Load environment variables first
import './load-env.js';

import { 
  readAppointments, 
  updateAppointmentStatus,
  deleteAppointment,
  addAppointment
} from '../lib/supabaseAppointmentUtils.js';

import { APPOINTMENT_STATUS } from '../lib/supabase.js';

console.log('🧪 Testing Core Appointment Operations...\n');

async function runBasicTests() {
  try {
    // Test 1: Read appointments
    console.log('1️⃣ Testing readAppointments...');
    const appointments = await readAppointments();
    console.log(`✅ Found ${appointments.length} appointments`);
    
    if (appointments.length === 0) {
      console.log('Creating a test appointment...');
      const testAppointment = {
        nome: 'Test',
        cognome: 'User',
        telefono: '1234567890',
        email: '<EMAIL>',
        servizio: '<PERSON><PERSON><PERSON> CAF',
        operatore: 'Qualsiasi',
        dataAppuntamento: '2025-01-25',
        orario: '10:00'
      };
      
      const created = await addAppointment(testAppointment);
      console.log(`✅ Created test appointment: ID ${created.id}`);
      
      // Re-fetch appointments
      const updatedAppointments = await readAppointments();
      console.log(`✅ Now found ${updatedAppointments.length} appointments`);
      
      return updatedAppointments;
    }
    
    return appointments;
    
  } catch (error) {
    console.error('❌ Read appointments test failed:', error);
    throw error;
  }
}

async function testStatusUpdate(appointments) {
  console.log('\n2️⃣ Testing updateAppointmentStatus...');
  
  if (appointments.length === 0) {
    console.log('⚠️ No appointments to test with');
    return;
  }
  
  const testAppointment = appointments[0];
  console.log(`Testing with appointment ID: ${testAppointment.id} (type: ${typeof testAppointment.id})`);
  console.log(`Current status: ${testAppointment.status}`);
  
  try {
    // Test status update
    const newStatus = testAppointment.status === 'confirmed' ? 'completed' : 'confirmed';
    console.log(`Attempting to change status to: ${newStatus}`);
    
    const result = await updateAppointmentStatus(testAppointment.id, newStatus);
    console.log('✅ Update successful:', result);
    
    // Test with string ID
    console.log('Testing with string ID...');
    const stringId = String(testAppointment.id);
    const result2 = await updateAppointmentStatus(stringId, testAppointment.status);
    console.log('✅ String ID update successful:', result2);
    
    return true;
    
  } catch (error) {
    console.error('❌ Update operation failed:', error);
    return false;
  }
}

async function testDeletion() {
  console.log('\n3️⃣ Testing deleteAppointment...');
  
  try {
    // Create a test appointment for deletion
    console.log('Creating a test appointment for deletion...');
    const testData = {
      nome: 'Delete',
      cognome: 'Test',
      telefono: '9876543210',
      email: '<EMAIL>',
      servizio: 'Servizi CAF',
      operatore: 'Qualsiasi',
      dataAppuntamento: '2025-01-26',
      orario: '11:00'
    };
    
    const testAppointment = await addAppointment(testData);
    console.log(`✅ Created test appointment for deletion: ID ${testAppointment.id}`);
    
    // Test deletion with numeric ID
    console.log(`Testing deletion with ID: ${testAppointment.id} (type: ${typeof testAppointment.id})`);
    const result = await deleteAppointment(testAppointment.id);
    console.log('✅ Delete successful:', result);
    
    // Test deletion with string ID (should fail gracefully)
    console.log('Testing deletion with non-existent string ID...');
    try {
      await deleteAppointment("99999");
      console.log('❌ Should have failed');
    } catch (error) {
      if (error.message.includes('No appointment found')) {
        console.log('✅ Correctly failed with "No appointment found" error');
      } else {
        console.log(`❌ Failed with unexpected error: ${error.message}`);
      }
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Delete operation failed:', error);
    return false;
  }
}

async function testEdgeCases() {
  console.log('\n4️⃣ Testing Edge Cases...');
  
  try {
    // Test with invalid IDs
    console.log('Testing with invalid appointment ID...');
    try {
      await updateAppointmentStatus(null, 'confirmed');
      console.log('❌ Should have failed with null ID');
    } catch (error) {
      console.log('✅ Correctly failed with null ID');
    }
    
    try {
      await updateAppointmentStatus('invalid', 'confirmed');
      console.log('❌ Should have failed with invalid ID');
    } catch (error) {
      console.log('✅ Correctly failed with invalid ID');
    }
    
    // Test with invalid status
    console.log('Testing with invalid status...');
    const appointments = await readAppointments();
    if (appointments.length > 0) {
      try {
        await updateAppointmentStatus(appointments[0].id, 'invalid_status');
        console.log('❌ Should have failed with invalid status');
      } catch (error) {
        console.log('✅ Correctly failed with invalid status');
      }
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Edge case tests failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  try {
    const appointments = await runBasicTests();
    const updateSuccess = await testStatusUpdate(appointments);
    const deleteSuccess = await testDeletion();
    const edgeCaseSuccess = await testEdgeCases();
    
    console.log('\n📋 Test Results:');
    console.log(`✅ Read appointments: PASSED`);
    console.log(`${updateSuccess ? '✅' : '❌'} Update status: ${updateSuccess ? 'PASSED' : 'FAILED'}`);
    console.log(`${deleteSuccess ? '✅' : '❌'} Delete appointment: ${deleteSuccess ? 'PASSED' : 'FAILED'}`);
    console.log(`${edgeCaseSuccess ? '✅' : '❌'} Edge cases: ${edgeCaseSuccess ? 'PASSED' : 'FAILED'}`);
    
    if (updateSuccess && deleteSuccess && edgeCaseSuccess) {
      console.log('\n🎉 All tests PASSED! Appointment operations should work correctly.');
    } else {
      console.log('\n❌ Some tests FAILED. Check the errors above.');
    }
    
    console.log('\n📝 Next Steps:');
    console.log('1. Start the development server: npm run dev');
    console.log('2. Test the admin panel operations in the browser');
    console.log('3. Check browser console for any frontend errors');
    console.log('4. Check server console for backend logs');
    
  } catch (error) {
    console.error('\n❌ Test suite failed:', error);
    process.exit(1);
  }
}

runAllTests().catch(console.error);
