// Load environment variables first
import './load-env.js';

import { createClient } from '@supabase/supabase-js';

console.log('🔗 Testing basic Supabase connection...');
console.log('SUPABASE_URL:', process.env.SUPABASE_URL);
console.log('SUPABASE_API length:', process.env.SUPABASE_API?.length);

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_API);

async function testConnection() {
  try {
    console.log('📡 Attempting to connect to Supabase...');

    const { data, error } = await supabase
      .from('appointments')
      .select('count', { count: 'exact', head: true });

    if (error) {
      console.error('❌ Connection failed:', error.message);
      console.error('📋 This might mean:');
      console.error('   1. The appointments table doesn\'t exist yet');
      console.error('   2. You need to run the database setup script first');
      console.error('   3. Check your Supabase credentials');
    } else {
      console.log('✅ Connection successful!');
      console.log('📊 Appointments count:', data || 0);
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testConnection().then(() => process.exit(0));
