/**
 * Verify Appointment ID 27 Issue
 * This script specifically tests the appointment ID 27 that was failing
 *
 * Usage: node scripts/verify-appointment-27.js
 */

// Load environment variables first
import './load-env.js';

import { supabase, supabaseAdmin, TABLES, convertSupabaseToJsonFormat } from '../lib/supabase.js';
import { readAppointments } from '../lib/supabaseAppointmentUtils.js';

console.log('🔍 Verifying Appointment ID 27 Issue...\n');

async function checkAppointmentInUI() {
  console.log('1️⃣ Checking how appointment 27 appears in UI data...');
  
  try {
    // Use the same function that the UI uses
    const appointments = await readAppointments();
    console.log(`Total appointments from readAppointments(): ${appointments.length}`);
    
    // Look for appointment with ID 27 (as string or number)
    const appointment27String = appointments.find(apt => apt.id === '27');
    const appointment27Number = appointments.find(apt => apt.id === 27);
    
    console.log('Appointment 27 (string search):', appointment27String);
    console.log('Appointment 27 (number search):', appointment27Number);
    
    // Show all appointment IDs and their types
    console.log('\nAll appointment IDs from UI:');
    appointments.slice(0, 10).forEach(apt => {
      console.log(`  - ID: ${apt.id} (type: ${typeof apt.id})`);
    });
    
    return { appointment27String, appointment27Number, allAppointments: appointments };
    
  } catch (error) {
    console.error('❌ Error checking UI data:', error);
    return null;
  }
}

async function checkAppointmentInDatabase() {
  console.log('\n2️⃣ Checking appointment 27 directly in database...');
  
  try {
    // Test with regular client (same as readAppointments uses)
    console.log('Testing with regular supabase client...');
    const { data: regularData, error: regularError } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .eq('id', 27);
    
    console.log('Regular client - ID 27 (number):', regularData);
    console.log('Regular client error:', regularError);
    
    // Test with string ID
    const { data: regularStringData, error: regularStringError } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .eq('id', '27');
    
    console.log('Regular client - ID "27" (string):', regularStringData);
    console.log('Regular client string error:', regularStringError);
    
    // Test with admin client
    console.log('\nTesting with admin supabase client...');
    const { data: adminData, error: adminError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .eq('id', 27);
    
    console.log('Admin client - ID 27 (number):', adminData);
    console.log('Admin client error:', adminError);
    
    // Test with admin client and string ID
    const { data: adminStringData, error: adminStringError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .eq('id', '27');
    
    console.log('Admin client - ID "27" (string):', adminStringData);
    console.log('Admin client string error:', adminStringError);
    
    return { regularData, adminData, regularStringData, adminStringData };
    
  } catch (error) {
    console.error('❌ Error checking database:', error);
    return null;
  }
}

async function checkRawDatabaseData() {
  console.log('\n3️⃣ Checking raw database data (without conversion)...');
  
  try {
    // Get raw data from database without conversion
    const { data: rawData, error: rawError } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (rawError) {
      console.error('❌ Error getting raw data:', rawError);
      return;
    }
    
    console.log('Raw database data (first 10 appointments):');
    rawData.forEach(apt => {
      console.log(`  - ID: ${apt.id} (type: ${typeof apt.id}) - ${apt.nome} ${apt.cognome}`);
    });
    
    // Check if ID 27 exists in raw data
    const rawAppointment27 = rawData.find(apt => apt.id === 27 || apt.id === '27');
    console.log('\nAppointment 27 in raw data:', rawAppointment27);
    
    // Test the conversion function
    if (rawData.length > 0) {
      console.log('\nTesting convertSupabaseToJsonFormat...');
      const converted = convertSupabaseToJsonFormat(rawData[0]);
      console.log('Original:', rawData[0]);
      console.log('Converted:', converted);
      console.log(`ID conversion: ${rawData[0].id} (${typeof rawData[0].id}) -> ${converted.id} (${typeof converted.id})`);
    }
    
    return rawData;
    
  } catch (error) {
    console.error('❌ Error checking raw data:', error);
    return null;
  }
}

async function testUpdateWithDifferentFormats() {
  console.log('\n4️⃣ Testing update operation with different ID formats...');
  
  try {
    // First, find any appointment to test with
    const { data: testData, error: testError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .select('id, status, nome, cognome')
      .limit(1);
    
    if (testError || !testData || testData.length === 0) {
      console.log('❌ No appointments found for testing');
      return;
    }
    
    const testAppointment = testData[0];
    console.log(`Testing with appointment: ID ${testAppointment.id}, ${testAppointment.nome} ${testAppointment.cognome}`);
    
    // Test update with numeric ID
    console.log('\nTesting update with numeric ID...');
    const { data: numericUpdateData, error: numericUpdateError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .update({ status: testAppointment.status }) // Same status, just testing
      .eq('id', testAppointment.id)
      .select();
    
    console.log('Numeric ID update result:', numericUpdateData);
    console.log('Numeric ID update error:', numericUpdateError);
    
    // Test update with string ID
    console.log('\nTesting update with string ID...');
    const stringId = String(testAppointment.id);
    const { data: stringUpdateData, error: stringUpdateError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .update({ status: testAppointment.status }) // Same status, just testing
      .eq('id', stringId)
      .select();
    
    console.log('String ID update result:', stringUpdateData);
    console.log('String ID update error:', stringUpdateError);
    
  } catch (error) {
    console.error('❌ Error testing updates:', error);
  }
}

// Run all tests
async function runAllTests() {
  try {
    const uiData = await checkAppointmentInUI();
    const dbData = await checkAppointmentInDatabase();
    const rawData = await checkRawDatabaseData();
    await testUpdateWithDifferentFormats();
    
    console.log('\n📋 Analysis Summary:');
    console.log('==================');
    
    if (uiData && (uiData.appointment27String || uiData.appointment27Number)) {
      console.log('✅ Appointment 27 found in UI data');
    } else {
      console.log('❌ Appointment 27 NOT found in UI data');
    }
    
    if (dbData && (dbData.regularData?.length > 0 || dbData.adminData?.length > 0)) {
      console.log('✅ Appointment 27 found in database');
    } else {
      console.log('❌ Appointment 27 NOT found in database');
    }
    
    console.log('\n🔍 Possible Issues:');
    console.log('1. ID type mismatch (string vs number)');
    console.log('2. Different database clients seeing different data');
    console.log('3. RLS policies blocking access');
    console.log('4. Data conversion issues');
    console.log('5. Appointment 27 may have been deleted or never existed');
    
  } catch (error) {
    console.error('\n❌ Test suite failed:', error);
    process.exit(1);
  }
}

runAllTests().catch(console.error);
