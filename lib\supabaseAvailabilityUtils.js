import { supabase, supabaseAdmin, TABLES, handleSupabaseError } from './supabase.js';

/**
 * Employee Availability Utility Functions
 * Handles CRUD operations for employee availability and special schedules
 */

// ========================================
// EMPLOYEE AVAILABILITY CRUD OPERATIONS
// ========================================

/**
 * Get employee availability for a specific employee
 * @param {string} employeeId - Employee UUID
 * @returns {Array} Array of availability records
 */
export const getEmployeeAvailability = async (employeeId) => {
  try {
    const { data, error } = await supabase
      .from(TABLES.EMPLOYEE_AVAILABILITY)
      .select('*')
      .eq('employee_id', employeeId)
      .order('day_of_week', { ascending: true })
      .order('start_time', { ascending: true });

    if (error) {
      handleSupabaseError(error, 'fetch employee availability');
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching employee availability:', error);
    throw error;
  }
};

/**
 * Create or update employee availability
 * @param {string} employeeId - Employee UUID
 * @param {Array} availabilityData - Array of availability records
 * @returns {Array} Created/updated availability records
 */
export const setEmployeeAvailability = async (employeeId, availabilityData) => {
  try {
    // First, delete existing availability for this employee
    const { error: deleteError } = await supabaseAdmin
      .from(TABLES.EMPLOYEE_AVAILABILITY)
      .delete()
      .eq('employee_id', employeeId);

    if (deleteError) {
      handleSupabaseError(deleteError, 'delete existing availability');
    }

    // Insert new availability records
    const recordsToInsert = availabilityData.map(record => ({
      employee_id: employeeId,
      day_of_week: record.day_of_week,
      start_time: record.start_time,
      end_time: record.end_time,
      is_available: record.is_available !== false, // Default to true
      break_start_time: record.break_disabled ? null : (record.break_start_time || null),
      break_end_time: record.break_disabled ? null : (record.break_end_time || null),
      break_disabled: record.break_disabled || false,
      notes: record.notes || null
    }));

    const { data, error } = await supabaseAdmin
      .from(TABLES.EMPLOYEE_AVAILABILITY)
      .insert(recordsToInsert)
      .select();

    if (error) {
      handleSupabaseError(error, 'create employee availability');
    }

    return data || [];
  } catch (error) {
    console.error('Error setting employee availability:', error);
    throw error;
  }
};

/**
 * Get employee special schedules
 * @param {string} employeeId - Employee UUID
 * @param {string} startDate - Start date (YYYY-MM-DD)
 * @param {string} endDate - End date (YYYY-MM-DD)
 * @returns {Array} Array of special schedule records
 */
export const getEmployeeSpecialSchedules = async (employeeId, startDate = null, endDate = null) => {
  try {
    let query = supabase
      .from(TABLES.EMPLOYEE_SPECIAL_SCHEDULES)
      .select('*')
      .eq('employee_id', employeeId);

    if (startDate) {
      query = query.gte('date', startDate);
    }
    if (endDate) {
      query = query.lte('date', endDate);
    }

    const { data, error } = await query.order('date', { ascending: true });

    if (error) {
      handleSupabaseError(error, 'fetch employee special schedules');
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching employee special schedules:', error);
    throw error;
  }
};

/**
 * Create or update employee special schedule
 * @param {Object} scheduleData - Special schedule data
 * @returns {Object} Created/updated special schedule record
 */
export const setEmployeeSpecialSchedule = async (scheduleData) => {
  try {
    const { data, error } = await supabaseAdmin
      .from(TABLES.EMPLOYEE_SPECIAL_SCHEDULES)
      .upsert({
        employee_id: scheduleData.employee_id,
        date: scheduleData.date,
        is_available: scheduleData.is_available !== undefined ? scheduleData.is_available : false,
        start_time: scheduleData.start_time || null,
        end_time: scheduleData.end_time || null,
        break_start_time: scheduleData.break_disabled ? null : (scheduleData.break_start_time || null),
        break_end_time: scheduleData.break_disabled ? null : (scheduleData.break_end_time || null),
        break_disabled: scheduleData.break_disabled || false,
        reason: scheduleData.reason || null,
        notes: scheduleData.notes || null
      }, {
        onConflict: 'employee_id,date'
      })
      .select()
      .single();

    if (error) {
      handleSupabaseError(error, 'create/update employee special schedule');
    }

    return data;
  } catch (error) {
    console.error('Error setting employee special schedule:', error);
    throw error;
  }
};

/**
 * Delete employee special schedule
 * @param {string} employeeId - Employee UUID
 * @param {string} date - Date (YYYY-MM-DD)
 * @returns {boolean} Success status
 */
export const deleteEmployeeSpecialSchedule = async (employeeId, date) => {
  try {
    const { error } = await supabaseAdmin
      .from(TABLES.EMPLOYEE_SPECIAL_SCHEDULES)
      .delete()
      .eq('employee_id', employeeId)
      .eq('date', date);

    if (error) {
      handleSupabaseError(error, 'delete employee special schedule');
    }

    return true;
  } catch (error) {
    console.error('Error deleting employee special schedule:', error);
    throw error;
  }
};

// ========================================
// AVAILABILITY CHECKING FUNCTIONS
// ========================================

/**
 * Check if an employee is available at a specific date and time
 * @param {string} employeeId - Employee UUID
 * @param {string} date - Date (YYYY-MM-DD)
 * @param {string} time - Time (HH:MM)
 * @returns {boolean} True if available, false otherwise
 */
export const isEmployeeAvailable = async (employeeId, date, time) => {
  try {
    const { data, error } = await supabase
      .rpc('is_employee_available', {
        p_employee_id: employeeId,
        p_date: date,
        p_time: time
      });

    if (error) {
      console.error('Error checking employee availability:', error);
      return false;
    }

    return data === true;
  } catch (error) {
    console.error('Error checking employee availability:', error);
    return false;
  }
};

/**
 * Get all available employees for a specific date and time
 * @param {string} date - Date (YYYY-MM-DD)
 * @param {string} time - Time (HH:MM)
 * @returns {Array} Array of available employees
 */
export const getAvailableEmployees = async (date, time) => {
  try {
    const { data, error } = await supabase
      .rpc('get_available_employees', {
        p_date: date,
        p_time: time
      });

    if (error) {
      handleSupabaseError(error, 'fetch available employees');
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching available employees:', error);
    return [];
  }
};

/**
 * Get available time slots for a specific employee on a specific date
 * @param {string} employeeId - Employee UUID
 * @param {string} date - Date (YYYY-MM-DD)
 * @param {Array} timeSlots - Array of time slots to check (default: standard time slots)
 * @returns {Array} Array of available time slots
 */
export const getEmployeeAvailableTimeSlots = async (employeeId, date, timeSlots = null) => {
  try {
    // Use default time slots if none provided
    const slotsToCheck = timeSlots || [
      '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00',
      '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00'
    ];

    const availableSlots = [];

    // Check each time slot
    for (const timeSlot of slotsToCheck) {
      const isAvailable = await isEmployeeAvailable(employeeId, date, timeSlot);
      if (isAvailable) {
        availableSlots.push(timeSlot);
      }
    }

    return availableSlots;
  } catch (error) {
    console.error('Error getting employee available time slots:', error);
    return [];
  }
};

// ========================================
// UTILITY FUNCTIONS
// ========================================

/**
 * Get day of week number from date
 * @param {string} date - Date (YYYY-MM-DD)
 * @returns {number} Day of week (0=Sunday, 1=Monday, etc.)
 */
export const getDayOfWeek = (date) => {
  return new Date(date).getDay();
};

/**
 * Get day name from day of week number
 * @param {number} dayOfWeek - Day of week (0=Sunday, 1=Monday, etc.)
 * @returns {string} Day name in Italian
 */
export const getDayName = (dayOfWeek) => {
  const dayNames = [
    'Domenica', 'Lunedì', 'Martedì', 'Mercoledì', 
    'Giovedì', 'Venerdì', 'Sabato'
  ];
  return dayNames[dayOfWeek] || 'Sconosciuto';
};

/**
 * Validate availability time range
 * @param {string} startTime - Start time (HH:MM)
 * @param {string} endTime - End time (HH:MM)
 * @param {string} breakStartTime - Break start time (HH:MM, optional)
 * @param {string} breakEndTime - Break end time (HH:MM, optional)
 * @param {boolean} breakDisabled - Whether break is disabled
 * @returns {Object} Validation result with isValid and errors
 */
export const validateAvailabilityTimeRange = (startTime, endTime, breakStartTime = null, breakEndTime = null, breakDisabled = false) => {
  const errors = [];

  // Check if end time is after start time
  if (endTime <= startTime) {
    errors.push('L\'orario di fine deve essere successivo all\'orario di inizio');
  }

  // Check break times if provided and break is not disabled
  if (!breakDisabled) {
    if (breakStartTime && breakEndTime) {
      if (breakEndTime <= breakStartTime) {
        errors.push('L\'orario di fine pausa deve essere successivo all\'orario di inizio pausa');
      }

      if (breakStartTime < startTime || breakEndTime > endTime) {
        errors.push('Gli orari di pausa devono essere compresi nell\'orario di lavoro');
      }
    } else if (breakStartTime || breakEndTime) {
      errors.push('Specificare sia l\'orario di inizio che di fine pausa');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Generate default availability for an employee (Monday-Friday, 9:00-18:00)
 * @returns {Array} Default availability records
 */
export const generateDefaultAvailability = () => {
  const defaultAvailability = [];

  // Monday to Friday (1-5)
  for (let day = 1; day <= 5; day++) {
    defaultAvailability.push({
      day_of_week: day,
      start_time: '09:00',
      end_time: '18:00',
      break_start_time: '12:00',
      break_end_time: '15:00',
      break_disabled: false,
      is_available: true,
      notes: null
    });
  }

  return defaultAvailability;
};
