# Employee Availability System - Implementation Guide

## Overview

This guide documents the complete implementation of the Employee Availability System for the CAF Appointment Booking System. The system allows detailed scheduling of employee availability, including regular weekly schedules, break times, and special schedules for holidays or exceptions.

## 🎯 Features Implemented

### 1. Database Schema
- **New `employee_availability` table** for regular weekly schedules
- **New `employee_special_schedules` table** for exceptions and special dates
- **PostgreSQL functions** for availability checking
- **Comprehensive constraints** and validation
- **Indexes** for optimal performance

### 2. Backend API
- **Employee availability CRUD operations**
- **Special schedule management**
- **Available employees filtering**
- **Enhanced availability checking**
- **Comprehensive validation**

### 3. Frontend Components
- **Enhanced Employee Form** with availability management
- **Dedicated Availability Form** for schedule configuration
- **Availability Management Component** for admin operations
- **Updated Appointment Forms** with employee filtering
- **Real-time availability checking**

### 4. Integration
- **Enhanced appointment booking** with availability constraints
- **Dynamic employee filtering** based on selected date/time
- **Improved user experience** with clear feedback
- **Backward compatibility** with existing system

## 🗄️ Database Schema

### Employee Availability Table
```sql
employee_availability (
  id UUID PRIMARY KEY,
  employee_id UUID REFERENCES employees(id),
  day_of_week INTEGER (0=Sunday, 1=Monday, ..., 6=Saturday),
  start_time TIME,
  end_time TIME,
  is_available BOOLEAN,
  break_start_time TIME (optional),
  break_end_time TIME (optional),
  notes TEXT,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

### Employee Special Schedules Table
```sql
employee_special_schedules (
  id UUID PRIMARY KEY,
  employee_id UUID REFERENCES employees(id),
  date DATE,
  is_available BOOLEAN,
  start_time TIME (optional),
  end_time TIME (optional),
  break_start_time TIME (optional),
  break_end_time TIME (optional),
  reason VARCHAR(255),
  notes TEXT,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

### Key Constraints
- **Time validation**: End time must be after start time
- **Break validation**: Break times must be within working hours
- **Unique constraints**: Prevent overlapping schedules
- **Foreign key constraints**: Ensure data integrity

## 🚀 Installation & Setup

### Step 1: Run Database Migration
```bash
# In your Supabase SQL Editor, run:
supabase-availability-migration.sql
```

This will:
- ✅ Create availability tables
- ✅ Set up indexes and constraints
- ✅ Configure Row Level Security
- ✅ Create utility functions and views
- ✅ Insert default availability for existing employees

### Step 2: Test the System
```bash
# Test availability system functionality
node scripts/test-availability-system.js
```

### Step 3: Start Development Server
```bash
npm run dev
```

## 📁 Files Created/Modified

### New Files Created:
```
supabase-availability-migration.sql          # Database migration for availability
lib/supabaseAvailabilityUtils.js            # Availability utility functions
app/api/employees/available/route.js         # Available employees API
app/api/admin/employees/[id]/availability/route.js    # Employee availability API
app/api/admin/employees/[id]/special-schedules/route.js # Special schedules API
components/EmployeeAvailabilityForm.js       # Availability form component
components/EmployeeAvailabilityManagement.js # Availability management component
scripts/test-availability-system.js          # Testing script
EMPLOYEE_AVAILABILITY_GUIDE.md               # This documentation
```

### Files Modified:
```
lib/supabase.js                              # Added new table constants
app/api/availability/route.js                # Enhanced with employee filtering
components/EmployeeForm.js                   # Added availability management
components/AppointmentForm.js                # Dynamic employee filtering
components/AdminBookingForm.js               # Dynamic employee filtering
```

## 🔧 API Endpoints

### Employee Availability Management

#### GET /api/admin/employees/[id]/availability
Get employee availability and special schedules.

**Query Parameters:**
- `start_date` (optional): Filter special schedules from date
- `end_date` (optional): Filter special schedules to date

#### PUT /api/admin/employees/[id]/availability
Update employee regular availability.

**Request Body:**
```json
{
  "availability": [
    {
      "day_of_week": 1,
      "start_time": "09:00",
      "end_time": "18:00",
      "break_start_time": "12:00",
      "break_end_time": "15:00",
      "is_available": true,
      "notes": "Regular Monday schedule"
    }
  ]
}
```

#### POST /api/admin/employees/[id]/special-schedules
Create or update special schedule.

**Request Body:**
```json
{
  "date": "2024-12-25",
  "is_available": false,
  "reason": "Christmas Holiday",
  "notes": "Office closed"
}
```

#### DELETE /api/admin/employees/[id]/special-schedules?date=YYYY-MM-DD
Delete special schedule for specific date.

### Available Employees

#### GET /api/employees/available
Get available employees for specific date/time.

**Query Parameters:**
- `date` (required): Date in YYYY-MM-DD format
- `time` (optional): Time in HH:MM format
- `specialization` (optional): Filter by specialization

**Response:**
```json
{
  "success": true,
  "date": "2024-01-15",
  "time": "10:00",
  "availableEmployees": [
    {
      "employee_id": "uuid",
      "employee_name": "John Doe",
      "role": "CAF Operator",
      "department": "CAF Services",
      "specializations": ["Servizi CAF", "Patronato"]
    }
  ],
  "count": 1
}
```

### Enhanced Availability Check

#### GET /api/availability?date=YYYY-MM-DD&employee_id=uuid
Enhanced availability check with employee filtering.

**New Response Fields:**
- `availableEmployees`: List of available employees for each time slot
- `summary.employeesAvailable`: Count of available employees

## 🎨 Frontend Components

### EmployeeAvailabilityForm
Comprehensive form for managing employee weekly schedules.

**Features:**
- Day-by-day availability configuration
- Working hours and break time settings
- Bulk operations (copy to all days, working days only)
- Real-time validation
- Notes for each day

**Usage:**
```jsx
<EmployeeAvailabilityForm
  employeeId={employee.id}
  initialAvailability={availability}
  onSave={handleSave}
  onCancel={handleCancel}
  isSubmitting={false}
/>
```

### EmployeeAvailabilityManagement
Admin component for managing all employee schedules.

**Features:**
- Employee selection interface
- Regular availability management
- Special schedules management
- Bulk operations
- Calendar integration

### Enhanced Employee Form
Updated employee form with integrated availability management.

**New Features:**
- Availability management section
- Load existing availability
- Save availability with employee data
- Toggle availability form visibility

### Updated Appointment Forms
Both user and admin appointment forms now filter employees by availability.

**Enhancements:**
- Real-time employee filtering
- Availability-based employee selection
- Clear feedback when no employees available
- Improved user experience

## 🛡️ Security & Validation

### Database Level
- **Row Level Security (RLS)** on all tables
- **Constraint validation** for time ranges
- **Foreign key constraints** for data integrity
- **Unique constraints** to prevent conflicts

### API Level
- **Authentication required** for admin operations
- **Input validation** for all parameters
- **Time format validation** (HH:MM)
- **Date format validation** (YYYY-MM-DD)
- **Business logic validation** (end > start, breaks within hours)

### Frontend Level
- **Real-time validation** in forms
- **User-friendly error messages**
- **Disabled states** for invalid operations
- **Loading states** for better UX

## 🧪 Testing

### Automated Tests
Run the comprehensive test suite:
```bash
node scripts/test-availability-system.js
```

**Test Coverage:**
- ✅ CRUD operations
- ✅ Availability checking functions
- ✅ Validation functions
- ✅ Edge cases and error handling
- ✅ Database constraints
- ✅ API endpoints

### Manual Testing Checklist
- [ ] Create employee with availability
- [ ] Update employee availability
- [ ] Create special schedules
- [ ] Book appointment with available employee
- [ ] Try booking with unavailable employee
- [ ] Test weekend/holiday restrictions
- [ ] Test break time restrictions
- [ ] Test form validations

## 🔄 Migration from Existing System

The availability system is designed to be backward compatible:

1. **Existing employees** get default availability (Mon-Fri, 9:00-18:00)
2. **Existing appointments** continue to work
3. **"Qualsiasi" employee** remains available for all times
4. **Gradual adoption** - configure availability as needed

## 📈 Performance Considerations

### Database Optimization
- **Indexes** on frequently queried columns
- **Efficient queries** using PostgreSQL functions
- **Minimal data transfer** with selective fields
- **Connection pooling** for concurrent requests

### Frontend Optimization
- **Lazy loading** of availability data
- **Caching** of employee lists
- **Debounced API calls** for real-time updates
- **Optimistic updates** for better UX

## 🚨 Troubleshooting

### Common Issues

**Employee not showing as available:**
1. Check regular availability for the day of week
2. Verify no special schedule overrides
3. Confirm employee is active
4. Check for existing appointments at that time

**Validation errors:**
1. Ensure end time is after start time
2. Verify break times are within working hours
3. Check date format (YYYY-MM-DD)
4. Confirm time format (HH:MM)

**API errors:**
1. Verify authentication token
2. Check employee ID exists
3. Validate request body format
4. Review server logs for details

### Debug Mode
Enable debug logging by setting environment variable:
```bash
DEBUG_AVAILABILITY=true
```

## 🔮 Future Enhancements

### Planned Features
- **Recurring special schedules** (e.g., every Monday off)
- **Team availability view** with calendar interface
- **Automatic conflict detection** for appointments
- **Email notifications** for schedule changes
- **Mobile-responsive** availability management
- **Bulk import/export** of schedules
- **Integration with external calendars**

### API Extensions
- **Bulk availability updates**
- **Availability templates**
- **Schedule conflict detection**
- **Availability statistics and reporting**

---

## 📞 Support

For issues or questions about the availability system:

1. **Check this documentation** first
2. **Run the test suite** to verify system health
3. **Review server logs** for error details
4. **Check database constraints** for validation issues

The availability system enhances the CAF appointment booking with intelligent employee scheduling while maintaining simplicity and reliability.
