'use client';

import { useState, useEffect } from 'react';
import EmployeeAvailabilityForm from './EmployeeAvailabilityForm';

export default function EmployeeAvailabilityManagement({ token }) {
  const [employees, setEmployees] = useState([]);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [employeeAvailability, setEmployeeAvailability] = useState([]);
  const [specialSchedules, setSpecialSchedules] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');
  const [showAvailabilityForm, setShowAvailabilityForm] = useState(false);
  const [showSpecialScheduleForm, setShowSpecialScheduleForm] = useState(false);
  const [newSpecialSchedule, setNewSpecialSchedule] = useState({
    date: '',
    is_available: false,
    start_time: '09:00',
    end_time: '18:00',
    break_start_time: '',
    break_end_time: '',
    break_disabled: false,
    reason: '',
    notes: ''
  });

  // Load employees
  useEffect(() => {
    loadEmployees();
  }, []);

  const loadEmployees = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/admin/employees', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const result = await response.json();
      if (result.success) {
        // Filter out "Qualsiasi" employee
        const realEmployees = result.data.filter(emp => emp.name !== 'Qualsiasi');
        setEmployees(realEmployees);
      } else {
        setMessage('Errore nel caricamento dei dipendenti');
      }
    } catch (error) {
      console.error('Error loading employees:', error);
      setMessage('Errore di connessione');
    } finally {
      setIsLoading(false);
    }
  };

  const loadEmployeeData = async (employeeId) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/employees/${employeeId}/availability`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const result = await response.json();
      if (result.success) {
        setEmployeeAvailability(result.data.availability || []);
        setSpecialSchedules(result.data.specialSchedules || []);
      } else {
        setMessage('Errore nel caricamento dei dati del dipendente');
      }
    } catch (error) {
      console.error('Error loading employee data:', error);
      setMessage('Errore di connessione');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmployeeSelect = (employee) => {
    setSelectedEmployee(employee);
    setMessage('');
    if (employee) {
      loadEmployeeData(employee.id);
    } else {
      setEmployeeAvailability([]);
      setSpecialSchedules([]);
    }
  };

  const handleAvailabilitySave = async (availabilityData) => {
    if (!selectedEmployee) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/admin/employees/${selectedEmployee.id}/availability`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ availability: availabilityData }),
      });

      const result = await response.json();
      if (result.success) {
        setEmployeeAvailability(result.data);
        setMessage('Disponibilità salvata con successo');
        setShowAvailabilityForm(false);
      } else {
        setMessage(result.message || 'Errore nel salvataggio della disponibilità');
      }
    } catch (error) {
      console.error('Error saving availability:', error);
      setMessage('Errore di connessione durante il salvataggio');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSpecialScheduleSave = async () => {
    if (!selectedEmployee || !newSpecialSchedule.date) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/admin/employees/${selectedEmployee.id}/special-schedules`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(newSpecialSchedule),
      });

      const result = await response.json();
      if (result.success) {
        // Reload special schedules
        loadEmployeeData(selectedEmployee.id);
        setMessage('Programma speciale salvato con successo');
        setShowSpecialScheduleForm(false);
        setNewSpecialSchedule({
          date: '',
          is_available: false,
          start_time: '09:00',
          end_time: '18:00',
          break_start_time: '',
          break_end_time: '',
          break_disabled: false,
          reason: '',
          notes: ''
        });
      } else {
        setMessage(result.message || 'Errore nel salvataggio del programma speciale');
      }
    } catch (error) {
      console.error('Error saving special schedule:', error);
      setMessage('Errore di connessione durante il salvataggio');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSpecialScheduleDelete = async (date) => {
    if (!selectedEmployee || !confirm('Sei sicuro di voler eliminare questo programma speciale?')) return;

    try {
      const response = await fetch(`/api/admin/employees/${selectedEmployee.id}/special-schedules?date=${date}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      if (result.success) {
        // Reload special schedules
        loadEmployeeData(selectedEmployee.id);
        setMessage('Programma speciale eliminato con successo');
      } else {
        setMessage(result.message || 'Errore nell\'eliminazione del programma speciale');
      }
    } catch (error) {
      console.error('Error deleting special schedule:', error);
      setMessage('Errore di connessione durante l\'eliminazione');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('it-IT');
  };

  const getDayName = (dayOfWeek) => {
    const days = ['Domenica', 'Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato'];
    return days[dayOfWeek];
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">
          Gestione Disponibilità Dipendenti
        </h2>
      </div>

      {/* Employee Selection */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Seleziona Dipendente
        </h3>
        
        {isLoading && !selectedEmployee ? (
          <div className="text-center py-4">
            <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-sm text-gray-600">Caricamento dipendenti...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {employees.map((employee) => (
              <button
                key={employee.id}
                onClick={() => handleEmployeeSelect(employee)}
                className={`p-4 border rounded-lg text-left transition-colors ${
                  selectedEmployee?.id === employee.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <div className="font-medium text-gray-900">{employee.name}</div>
                <div className="text-sm text-gray-600">{employee.role}</div>
                <div className="text-sm text-gray-500">{employee.department}</div>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Message */}
      {message && (
        <div className={`p-4 rounded-lg ${
          message.includes('Errore') 
            ? 'bg-red-50 border border-red-200 text-red-700'
            : 'bg-green-50 border border-green-200 text-green-700'
        }`}>
          {message}
        </div>
      )}

      {/* Employee Details */}
      {selectedEmployee && (
        <div className="space-y-6">
          {/* Current Availability */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Disponibilità Settimanale - {selectedEmployee.name}
              </h3>
              <button
                onClick={() => setShowAvailabilityForm(!showAvailabilityForm)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {showAvailabilityForm ? 'Nascondi' : 'Modifica Orari'}
              </button>
            </div>

            {isLoading ? (
              <div className="text-center py-4">
                <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <p className="mt-2 text-sm text-gray-600">Caricamento disponibilità...</p>
              </div>
            ) : employeeAvailability.length === 0 ? (
              <p className="text-gray-600">Nessuna disponibilità configurata</p>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {employeeAvailability.map((avail) => (
                  <div key={avail.id} className="border rounded-lg p-4">
                    <div className="font-medium text-gray-900">
                      {getDayName(avail.day_of_week)}
                    </div>
                    <div className="text-sm text-gray-600">
                      {avail.start_time} - {avail.end_time}
                    </div>
                    {avail.break_disabled ? (
                      <div className="text-sm text-gray-500 italic">
                        Orario continuato (senza pausa)
                      </div>
                    ) : avail.break_start_time && avail.break_end_time ? (
                      <div className="text-sm text-gray-500">
                        Pausa: {avail.break_start_time} - {avail.break_end_time}
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500">
                        Nessuna pausa configurata
                      </div>
                    )}
                    {avail.notes && (
                      <div className="text-sm text-gray-500 mt-1">
                        {avail.notes}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {showAvailabilityForm && (
              <div className="mt-6 border-t pt-6">
                <EmployeeAvailabilityForm
                  employeeId={selectedEmployee.id}
                  initialAvailability={employeeAvailability}
                  onSave={handleAvailabilitySave}
                  onCancel={() => setShowAvailabilityForm(false)}
                  isSubmitting={isSubmitting}
                />
              </div>
            )}
          </div>

          {/* Special Schedules */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Programmi Speciali - {selectedEmployee.name}
              </h3>
              <button
                onClick={() => setShowSpecialScheduleForm(!showSpecialScheduleForm)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                {showSpecialScheduleForm ? 'Annulla' : 'Aggiungi Programma'}
              </button>
            </div>

            {/* Special Schedule Form */}
            {showSpecialScheduleForm && (
              <div className="mb-6 p-4 border rounded-lg bg-gray-50">
                <h4 className="font-medium text-gray-900 mb-4">Nuovo Programma Speciale</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Data
                    </label>
                    <input
                      type="date"
                      value={newSpecialSchedule.date}
                      onChange={(e) => setNewSpecialSchedule(prev => ({ ...prev, date: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Motivo
                    </label>
                    <select
                      value={newSpecialSchedule.reason}
                      onChange={(e) => setNewSpecialSchedule(prev => ({ ...prev, reason: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Seleziona motivo</option>
                      <option value="Ferie">Ferie</option>
                      <option value="Malattia">Malattia</option>
                      <option value="Permesso">Permesso</option>
                      <option value="Formazione">Formazione</option>
                      <option value="Orario Speciale">Orario Speciale</option>
                      <option value="Altro">Altro</option>
                    </select>
                  </div>

                  <div className="md:col-span-2">
                    <label className="flex items-center space-x-2 mb-4">
                      <input
                        type="checkbox"
                        checked={newSpecialSchedule.is_available}
                        onChange={(e) => setNewSpecialSchedule(prev => ({ ...prev, is_available: e.target.checked }))}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="text-sm font-medium text-gray-700">Disponibile in questo giorno</span>
                    </label>
                  </div>

                  {newSpecialSchedule.is_available && (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Orario Inizio
                        </label>
                        <input
                          type="time"
                          value={newSpecialSchedule.start_time}
                          onChange={(e) => setNewSpecialSchedule(prev => ({ ...prev, start_time: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Orario Fine
                        </label>
                        <input
                          type="time"
                          value={newSpecialSchedule.end_time}
                          onChange={(e) => setNewSpecialSchedule(prev => ({ ...prev, end_time: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      <div className="md:col-span-2">
                        <label className="flex items-center space-x-2 mb-3">
                          <input
                            type="checkbox"
                            checked={newSpecialSchedule.break_disabled}
                            onChange={(e) => setNewSpecialSchedule(prev => ({
                              ...prev,
                              break_disabled: e.target.checked,
                              break_start_time: e.target.checked ? '' : prev.break_start_time,
                              break_end_time: e.target.checked ? '' : prev.break_end_time
                            }))}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="text-sm font-medium text-gray-700">Disabilita pausa (orario continuato)</span>
                        </label>
                      </div>

                      {!newSpecialSchedule.break_disabled && (
                        <>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Pausa Inizio (opzionale)
                            </label>
                            <input
                              type="time"
                              value={newSpecialSchedule.break_start_time}
                              onChange={(e) => setNewSpecialSchedule(prev => ({ ...prev, break_start_time: e.target.value }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Pausa Fine (opzionale)
                            </label>
                            <input
                              type="time"
                              value={newSpecialSchedule.break_end_time}
                              onChange={(e) => setNewSpecialSchedule(prev => ({ ...prev, break_end_time: e.target.value }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                          </div>
                        </>
                      )}
                    </>
                  )}

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Note (opzionale)
                    </label>
                    <textarea
                      value={newSpecialSchedule.notes}
                      onChange={(e) => setNewSpecialSchedule(prev => ({ ...prev, notes: e.target.value }))}
                      rows={2}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Note aggiuntive..."
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-4">
                  <button
                    type="button"
                    onClick={() => setShowSpecialScheduleForm(false)}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Annulla
                  </button>
                  <button
                    type="button"
                    onClick={handleSpecialScheduleSave}
                    disabled={isSubmitting || !newSpecialSchedule.date}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                  >
                    {isSubmitting ? 'Salvando...' : 'Salva Programma'}
                  </button>
                </div>
              </div>
            )}

            {/* Special Schedules List */}
            {specialSchedules.length === 0 ? (
              <p className="text-gray-600">Nessun programma speciale configurato</p>
            ) : (
              <div className="space-y-3">
                {specialSchedules.map((schedule) => (
                  <div key={schedule.id} className="border rounded-lg p-4 flex justify-between items-start">
                    <div>
                      <div className="font-medium text-gray-900">
                        {formatDate(schedule.date)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {schedule.is_available ? (
                          <>
                            Disponibile: {schedule.start_time} - {schedule.end_time}
                            {schedule.break_disabled ? (
                              <span className="ml-2 italic">
                                (Orario continuato)
                              </span>
                            ) : schedule.break_start_time && schedule.break_end_time ? (
                              <span className="ml-2">
                                (Pausa: {schedule.break_start_time} - {schedule.break_end_time})
                              </span>
                            ) : null}
                          </>
                        ) : (
                          'Non disponibile'
                        )}
                      </div>
                      {schedule.reason && (
                        <div className="text-sm text-gray-500">
                          Motivo: {schedule.reason}
                        </div>
                      )}
                      {schedule.notes && (
                        <div className="text-sm text-gray-500">
                          Note: {schedule.notes}
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => handleSpecialScheduleDelete(schedule.date)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Elimina
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
