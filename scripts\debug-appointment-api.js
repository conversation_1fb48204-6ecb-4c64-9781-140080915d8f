/**
 * Debug Script for Appointment API Issues
 * This script tests the appointment API endpoints directly to identify issues
 *
 * Usage: node scripts/debug-appointment-api.js
 */

// Load environment variables first
import './load-env.js';

import { 
  readAppointments, 
  updateAppointmentStatus,
  deleteAppointment,
  addAppointment
} from '../lib/supabaseAppointmentUtils.js';

import { validateAdminCredentials } from '../lib/supabaseAuthUtils.js';
import { APPOINTMENT_STATUS } from '../lib/supabase.js';

console.log('🔍 Debugging Appointment API Issues...\n');

async function testDatabaseDirectly() {
  console.log('📊 Testing database operations directly...');
  
  try {
    // Test 1: Read appointments
    console.log('\n1️⃣ Testing readAppointments...');
    const appointments = await readAppointments();
    console.log(`✅ Found ${appointments.length} appointments`);
    
    if (appointments.length === 0) {
      console.log('⚠️ No appointments found - creating a test appointment first');
      
      const testAppointment = {
        nome: 'Debug',
        cognome: 'Test',
        telefono: '1234567890',
        email: '<EMAIL>',
        servizio: 'Servizi CAF',
        operatore: 'Qualsiasi',
        dataAppuntamento: '2025-01-20',
        orario: '10:00'
      };
      
      const created = await addAppointment(testAppointment);
      console.log(`✅ Created test appointment: ID ${created.id}`);
      
      // Re-fetch appointments
      const updatedAppointments = await readAppointments();
      console.log(`✅ Now found ${updatedAppointments.length} appointments`);
      
      return updatedAppointments;
    }
    
    return appointments;
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
    throw error;
  }
}

async function testUpdateOperation(appointments) {
  console.log('\n2️⃣ Testing updateAppointmentStatus...');
  
  if (appointments.length === 0) {
    console.log('⚠️ No appointments to test with');
    return;
  }
  
  const testAppointment = appointments[0];
  console.log(`Testing with appointment ID: ${testAppointment.id}`);
  console.log(`Current status: ${testAppointment.status}`);
  
  try {
    // Test status update
    const newStatus = testAppointment.status === 'confirmed' ? 'completed' : 'confirmed';
    console.log(`Attempting to change status to: ${newStatus}`);
    
    const result = await updateAppointmentStatus(testAppointment.id, newStatus);
    console.log('✅ Update successful:', result);
    
    // Verify the update
    const updatedAppointments = await readAppointments();
    const updatedAppointment = updatedAppointments.find(apt => apt.id === testAppointment.id);
    
    if (updatedAppointment && updatedAppointment.status === newStatus) {
      console.log('✅ Status update verified in database');
    } else {
      console.log('❌ Status update not reflected in database');
    }
    
    return updatedAppointment;
    
  } catch (error) {
    console.error('❌ Update operation failed:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack
    });
    throw error;
  }
}

async function testDeleteOperation(appointments) {
  console.log('\n3️⃣ Testing deleteAppointment...');
  
  if (appointments.length === 0) {
    console.log('⚠️ No appointments to test with');
    return;
  }
  
  // Find a test appointment or create one
  let testAppointment = appointments.find(apt => 
    apt.nome === 'Debug' || apt.nome === 'Test' || apt.email?.includes('test')
  );
  
  if (!testAppointment) {
    console.log('Creating a test appointment for deletion...');
    const testData = {
      nome: 'Delete',
      cognome: 'Test',
      telefono: '9876543210',
      email: '<EMAIL>',
      servizio: 'Servizi CAF',
      operatore: 'Qualsiasi',
      dataAppuntamento: '2025-01-21',
      orario: '11:00'
    };
    
    testAppointment = await addAppointment(testData);
    console.log(`✅ Created test appointment for deletion: ID ${testAppointment.id}`);
  }
  
  console.log(`Testing deletion with appointment ID: ${testAppointment.id}`);
  
  try {
    const result = await deleteAppointment(testAppointment.id);
    console.log('✅ Delete successful:', result);
    
    // Verify the deletion
    const remainingAppointments = await readAppointments();
    const deletedAppointment = remainingAppointments.find(apt => apt.id === testAppointment.id);
    
    if (!deletedAppointment) {
      console.log('✅ Deletion verified - appointment removed from database');
    } else {
      console.log('❌ Deletion failed - appointment still exists in database');
    }
    
  } catch (error) {
    console.error('❌ Delete operation failed:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack
    });
    throw error;
  }
}

async function testAPIEndpoints() {
  console.log('\n🌐 Testing API endpoints via HTTP...');
  
  try {
    // First, get a valid admin token
    console.log('Getting admin token...');
    const adminUser = await validateAdminCredentials('admin', 'caf2024!');
    
    if (!adminUser || !adminUser.token) {
      console.log('❌ Could not get admin token - check admin credentials');
      return;
    }
    
    console.log('✅ Got admin token');
    
    const baseUrl = 'http://localhost:3000';
    const headers = {
      'Authorization': `Bearer ${adminUser.token}`,
      'Content-Type': 'application/json'
    };
    
    // Test GET appointments
    console.log('\n📡 Testing GET /api/admin/appointments...');
    try {
      const response = await fetch(`${baseUrl}/api/admin/appointments`, {
        headers: { 'Authorization': `Bearer ${adminUser.token}` }
      });
      
      console.log(`Response status: ${response.status}`);
      const result = await response.json();
      
      if (response.ok) {
        console.log(`✅ GET appointments successful - found ${result.data?.length || 0} appointments`);
        
        if (result.data && result.data.length > 0) {
          const testAppointment = result.data[0];
          console.log(`Sample appointment: ID ${testAppointment.id}, Status: ${testAppointment.status}`);
          
          // Test PUT (status update)
          console.log('\n📡 Testing PUT /api/admin/appointments (status update)...');
          const newStatus = testAppointment.status === 'confirmed' ? 'completed' : 'confirmed';
          
          const updateResponse = await fetch(`${baseUrl}/api/admin/appointments`, {
            method: 'PUT',
            headers,
            body: JSON.stringify({
              appointmentId: testAppointment.id,
              status: newStatus
            })
          });
          
          console.log(`Update response status: ${updateResponse.status}`);
          const updateResult = await updateResponse.json();
          console.log('Update response:', updateResult);
          
          if (updateResponse.ok) {
            console.log('✅ PUT appointment status successful');
          } else {
            console.log('❌ PUT appointment status failed');
          }
          
          // Test DELETE
          console.log('\n📡 Testing DELETE /api/admin/appointments...');
          
          // Create a test appointment for deletion
          const createResponse = await fetch(`${baseUrl}/api/admin/appointments/create`, {
            method: 'POST',
            headers,
            body: JSON.stringify({
              nome: 'API',
              cognome: 'Test',
              telefono: '5555555555',
              email: '<EMAIL>',
              servizio: 'Servizi CAF',
              operatore: 'Qualsiasi',
              dataAppuntamento: '2025-01-22',
              orario: '12:00'
            })
          });
          
          if (createResponse.ok) {
            const createResult = await createResponse.json();
            const testId = createResult.appointment?.id;
            
            if (testId) {
              console.log(`Created test appointment for deletion: ID ${testId}`);
              
              const deleteResponse = await fetch(`${baseUrl}/api/admin/appointments?id=${testId}`, {
                method: 'DELETE',
                headers: { 'Authorization': `Bearer ${adminUser.token}` }
              });
              
              console.log(`Delete response status: ${deleteResponse.status}`);
              const deleteResult = await deleteResponse.json();
              console.log('Delete response:', deleteResult);
              
              if (deleteResponse.ok) {
                console.log('✅ DELETE appointment successful');
              } else {
                console.log('❌ DELETE appointment failed');
              }
            }
          }
        }
      } else {
        console.log('❌ GET appointments failed:', result);
      }
      
    } catch (fetchError) {
      console.log('❌ API endpoint test failed - server may not be running');
      console.log('Error:', fetchError.message);
    }
    
  } catch (error) {
    console.error('❌ API endpoint test failed:', error);
  }
}

async function checkDatabaseSchema() {
  console.log('\n🗄️ Checking database schema...');
  
  try {
    const { supabase } = await import('../lib/supabase.js');
    
    // Check appointments table structure
    const { data, error } = await supabase
      .from('appointments')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ Database schema check failed:', error);
    } else {
      console.log('✅ Database connection successful');
      if (data && data.length > 0) {
        console.log('Sample appointment structure:', Object.keys(data[0]));
      }
    }
    
  } catch (error) {
    console.error('❌ Database schema check failed:', error);
  }
}

// Run all tests
async function runAllTests() {
  try {
    await checkDatabaseSchema();
    const appointments = await testDatabaseDirectly();
    await testUpdateOperation(appointments);
    await testDeleteOperation(appointments);
    await testAPIEndpoints();
    
    console.log('\n📋 Debug Summary:');
    console.log('✅ Database operations tested');
    console.log('✅ API endpoints tested');
    console.log('✅ Error scenarios tested');
    console.log('\n🔍 Check the output above for any specific errors or failures');
    
  } catch (error) {
    console.error('\n❌ Debug tests failed:', error);
    process.exit(1);
  }
}

runAllTests().catch(console.error);
