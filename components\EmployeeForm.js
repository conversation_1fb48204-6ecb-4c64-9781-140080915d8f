'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';

const SERVICE_TYPES = [
  'Servizi CAF',
  'Patronato',
  'Avvocato',
  'Medico',
  'Prestiti',
  'Immigrazione',
  'Altri Servizi'
];

const DEPARTMENTS = [
  'Servizi CAF',
  'Patronato',
  'Servizi Legali',
  'Servizi Medici',
  'Prestiti e Finanza',
  'Immigrazione',
  'Amministrazione',
  'Tutti i Servizi'
];

export default function EmployeeForm({ 
  employee = null, 
  token, 
  onSave, 
  onCancel,
  isOpen 
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [selectedSpecializations, setSelectedSpecializations] = useState([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm();

  const isEditing = !!employee;

  // Initialize form when employee data changes
  useEffect(() => {
    if (employee) {
      // Set form values for editing
      setValue('name', employee.name || '');
      setValue('email', employee.email || '');
      setValue('role', employee.role || '');
      setValue('department', employee.department || '');
      setValue('phone', employee.phone || '');
      setValue('is_active', employee.is_active !== undefined ? employee.is_active : true);
      setValue('notes', employee.notes || '');
      
      // Set specializations
      setSelectedSpecializations(employee.specializations || []);
    } else {
      // Reset form for new employee
      reset();
      setSelectedSpecializations([]);
      setValue('is_active', true);
    }
    setSubmitMessage('');
  }, [employee, setValue, reset]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      reset();
      setSelectedSpecializations([]);
      setSubmitMessage('');
    }
  }, [isOpen, reset]);

  const handleSpecializationChange = (specialization) => {
    setSelectedSpecializations(prev => {
      if (prev.includes(specialization)) {
        return prev.filter(s => s !== specialization);
      } else {
        return [...prev, specialization];
      }
    });
  };

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    setSubmitMessage('');

    try {
      const employeeData = {
        ...data,
        specializations: selectedSpecializations
      };

      const url = isEditing 
        ? `/api/admin/employees/${employee.id}`
        : '/api/admin/employees';
      
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(employeeData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Errore nel salvataggio del dipendente');
      }

      setSubmitMessage(result.message || 'Dipendente salvato con successo');
      
      // Call onSave callback
      if (onSave) {
        onSave(result.data);
      }

      // Reset form if creating new employee
      if (!isEditing) {
        reset();
        setSelectedSpecializations([]);
      }

    } catch (error) {
      console.error('Error saving employee:', error);
      setSubmitMessage(`Errore: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-[var(--primary-text)]">
              {isEditing ? 'Modifica Dipendente' : 'Nuovo Dipendente'}
            </h2>
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Nome *
              </label>
              <input
                type="text"
                id="name"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                {...register('name', { 
                  required: 'Il nome è obbligatorio',
                  minLength: { value: 2, message: 'Il nome deve avere almeno 2 caratteri' }
                })}
              />
              {errors.name && (
                <p className="text-red-600 text-sm mt-1">{errors.name.message}</p>
              )}
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                type="email"
                id="email"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                {...register('email', {
                  pattern: {
                    value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                    message: 'Formato email non valido'
                  }
                })}
              />
              {errors.email && (
                <p className="text-red-600 text-sm mt-1">{errors.email.message}</p>
              )}
            </div>

            {/* Role */}
            <div>
              <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                Ruolo
              </label>
              <input
                type="text"
                id="role"
                placeholder="es. Operatore CAF, Consulente Legale"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                {...register('role')}
              />
            </div>

            {/* Department */}
            <div>
              <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-1">
                Dipartimento
              </label>
              <select
                id="department"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                {...register('department')}
              >
                <option value="">Seleziona dipartimento</option>
                {DEPARTMENTS.map((dept) => (
                  <option key={dept} value={dept}>
                    {dept}
                  </option>
                ))}
              </select>
            </div>

            {/* Phone */}
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                Telefono
              </label>
              <input
                type="tel"
                id="phone"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                {...register('phone')}
              />
            </div>

            {/* Specializations */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Specializzazioni
              </label>
              <div className="grid grid-cols-2 gap-2">
                {SERVICE_TYPES.map((service) => (
                  <label key={service} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={selectedSpecializations.includes(service)}
                      onChange={() => handleSpecializationChange(service)}
                      className="rounded border-gray-300 text-[var(--primary-red)] focus:ring-[var(--primary-red)]"
                    />
                    <span className="text-sm">{service}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Active Status */}
            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  {...register('is_active')}
                  className="rounded border-gray-300 text-[var(--primary-red)] focus:ring-[var(--primary-red)]"
                />
                <span className="text-sm font-medium text-gray-700">Dipendente attivo</span>
              </label>
            </div>

            {/* Notes */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Note
              </label>
              <textarea
                id="notes"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                {...register('notes')}
              />
            </div>

            {/* Submit Message */}
            {submitMessage && (
              <div className={`p-3 rounded-lg ${
                submitMessage.includes('Errore') 
                  ? 'bg-red-50 border border-red-200 text-red-700'
                  : 'bg-green-50 border border-green-200 text-green-700'
              }`}>
                {submitMessage}
              </div>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Annulla
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="bg-[var(--primary-red)] hover:bg-[var(--hover-accent)] text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
              >
                {isSubmitting ? 'Salvando...' : (isEditing ? 'Aggiorna' : 'Crea')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
