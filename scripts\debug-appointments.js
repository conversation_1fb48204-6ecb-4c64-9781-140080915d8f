// Load environment variables first
import './load-env.js';

import { supabase, TABLES } from '../lib/supabase.js';

console.log('🔍 Debugging appointments in Supabase...');

try {
  // Get all appointments directly from Supabase
  const { data: appointments, error } = await supabase
    .from(TABLES.APPOINTMENTS)
    .select('*')
    .order('id', { ascending: true });

  if (error) {
    console.error('❌ Error fetching appointments:', error);
    process.exit(1);
  }

  console.log(`\n📊 Found ${appointments.length} appointments in Supabase:`);
  
  appointments.forEach((appointment, index) => {
    console.log(`\n${index + 1}. Raw Supabase record:`);
    console.log(`   ID: ${appointment.id} (type: ${typeof appointment.id})`);
    console.log(`   Name: ${appointment.nome} ${appointment.cognome}`);
    console.log(`   Date: ${appointment.data_appuntamento} at ${appointment.orario}`);
    console.log(`   Status: ${appointment.status}`);
    console.log(`   Service: ${appointment.servizio}`);
    console.log(`   Created: ${appointment.created_at}`);
  });

  // Test update operation
  if (appointments.length > 0) {
    const testId = appointments[0].id;
    console.log(`\n🧪 Testing update operation with ID ${testId}...`);
    
    const { data: updateData, error: updateError } = await supabase
      .from(TABLES.APPOINTMENTS)
      .update({ status: 'confirmed' })
      .eq('id', testId)
      .select()
      .single();

    if (updateError) {
      console.error('❌ Update test failed:', updateError);
    } else {
      console.log('✅ Update test successful:', updateData);
    }

    // Test delete operation (but don't actually delete)
    console.log(`\n🧪 Testing delete query (dry run) with ID ${testId}...`);
    
    const { data: deleteData, error: deleteError } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .eq('id', testId)
      .single();

    if (deleteError) {
      console.error('❌ Delete query test failed:', deleteError);
    } else {
      console.log('✅ Delete query test successful - record found:', deleteData);
    }
  }

} catch (error) {
  console.error('❌ Error:', error.message);
}

process.exit(0);
