-- Update script to add break_disabled field to existing availability tables
-- Run this if you already have the availability system and want to add the "disable break" feature

-- Add break_disabled column to employee_availability table
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'employee_availability' 
        AND column_name = 'break_disabled'
    ) THEN
        ALTER TABLE employee_availability 
        ADD COLUMN break_disabled BOOLEAN DEFAULT false;
        
        RAISE NOTICE 'Added break_disabled column to employee_availability table';
    ELSE
        RAISE NOTICE 'break_disabled column already exists in employee_availability table';
    END IF;
END $$;

-- Add break_disabled column to employee_special_schedules table
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'employee_special_schedules' 
        AND column_name = 'break_disabled'
    ) THEN
        ALTER TABLE employee_special_schedules 
        ADD COLUMN break_disabled BOOLEAN DEFAULT false;
        
        RAISE NOTICE 'Added break_disabled column to employee_special_schedules table';
    ELSE
        RAISE NOTICE 'break_disabled column already exists in employee_special_schedules table';
    END IF;
END $$;

-- Update constraints for employee_availability table
DO $$
BEGIN
    -- Drop existing constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'employee_availability' 
        AND constraint_name = 'valid_break_times'
    ) THEN
        ALTER TABLE employee_availability DROP CONSTRAINT valid_break_times;
        RAISE NOTICE 'Dropped old valid_break_times constraint from employee_availability';
    END IF;
    
    -- Add new constraint that considers break_disabled
    ALTER TABLE employee_availability 
    ADD CONSTRAINT valid_break_times CHECK (
        break_disabled = true OR
        (break_start_time IS NULL AND break_end_time IS NULL) OR
        (break_start_time IS NOT NULL AND break_end_time IS NOT NULL AND 
         break_end_time > break_start_time AND
         break_start_time >= start_time AND break_end_time <= end_time)
    );
    
    RAISE NOTICE 'Added new valid_break_times constraint to employee_availability';
END $$;

-- Update constraints for employee_special_schedules table
DO $$
BEGIN
    -- Drop existing constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'employee_special_schedules' 
        AND constraint_name = 'valid_special_break_times'
    ) THEN
        ALTER TABLE employee_special_schedules DROP CONSTRAINT valid_special_break_times;
        RAISE NOTICE 'Dropped old valid_special_break_times constraint from employee_special_schedules';
    END IF;
    
    -- Add new constraint that considers break_disabled
    ALTER TABLE employee_special_schedules 
    ADD CONSTRAINT valid_special_break_times CHECK (
        break_disabled = true OR
        (break_start_time IS NULL AND break_end_time IS NULL) OR
        (break_start_time IS NOT NULL AND break_end_time IS NOT NULL AND 
         break_end_time > break_start_time AND
         break_start_time >= start_time AND break_end_time <= end_time)
    );
    
    RAISE NOTICE 'Added new valid_special_break_times constraint to employee_special_schedules';
END $$;

-- Update the availability checking function
CREATE OR REPLACE FUNCTION is_employee_available(
    p_employee_id UUID,
    p_date DATE,
    p_time TIME
) RETURNS BOOLEAN AS $$
DECLARE
    v_day_of_week INTEGER;
    v_special_schedule RECORD;
    v_regular_availability RECORD;
BEGIN
    -- Get day of week (0=Sunday, 1=Monday, etc.)
    v_day_of_week := EXTRACT(DOW FROM p_date);
    
    -- Check for special schedule first (overrides regular availability)
    SELECT * INTO v_special_schedule
    FROM employee_special_schedules
    WHERE employee_id = p_employee_id AND date = p_date;
    
    IF FOUND THEN
        -- Special schedule exists
        IF NOT v_special_schedule.is_available THEN
            RETURN FALSE;
        END IF;
        
        -- Check if time is within special schedule hours
        IF p_time < v_special_schedule.start_time OR p_time >= v_special_schedule.end_time THEN
            RETURN FALSE;
        END IF;
        
        -- Check if time is during break (only if break is not disabled)
        IF NOT v_special_schedule.break_disabled AND
           v_special_schedule.break_start_time IS NOT NULL AND 
           p_time >= v_special_schedule.break_start_time AND 
           p_time < v_special_schedule.break_end_time THEN
            RETURN FALSE;
        END IF;
        
        RETURN TRUE;
    END IF;
    
    -- No special schedule, check regular availability
    SELECT * INTO v_regular_availability
    FROM employee_availability
    WHERE employee_id = p_employee_id 
      AND day_of_week = v_day_of_week
      AND is_available = true
      AND p_time >= start_time 
      AND p_time < end_time;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Check if time is during break (only if break is not disabled)
    IF NOT v_regular_availability.break_disabled AND
       v_regular_availability.break_start_time IS NOT NULL AND 
       p_time >= v_regular_availability.break_start_time AND 
       p_time < v_regular_availability.break_end_time THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Update the employee availability view
CREATE OR REPLACE VIEW employee_availability_view AS
SELECT 
    ea.id,
    ea.employee_id,
    e.name as employee_name,
    ea.day_of_week,
    CASE ea.day_of_week
        WHEN 0 THEN 'Domenica'
        WHEN 1 THEN 'Lunedì'
        WHEN 2 THEN 'Martedì'
        WHEN 3 THEN 'Mercoledì'
        WHEN 4 THEN 'Giovedì'
        WHEN 5 THEN 'Venerdì'
        WHEN 6 THEN 'Sabato'
    END as day_name,
    ea.start_time,
    ea.end_time,
    ea.break_start_time,
    ea.break_end_time,
    ea.break_disabled,
    ea.is_available,
    ea.notes,
    ea.created_at,
    ea.updated_at
FROM employee_availability ea
JOIN employees e ON ea.employee_id = e.id
WHERE e.is_active = true
ORDER BY e.name, ea.day_of_week, ea.start_time;

-- Clear any existing break times where break is disabled (data cleanup)
UPDATE employee_availability 
SET break_start_time = NULL, break_end_time = NULL 
WHERE break_disabled = true AND (break_start_time IS NOT NULL OR break_end_time IS NOT NULL);

UPDATE employee_special_schedules 
SET break_start_time = NULL, break_end_time = NULL 
WHERE break_disabled = true AND (break_start_time IS NOT NULL OR break_end_time IS NOT NULL);

-- Grant permissions (if needed)
GRANT SELECT ON employee_availability_view TO anon, authenticated;

RAISE NOTICE 'Break disabled feature successfully added to availability system!';
RAISE NOTICE 'You can now use the "Disabilita pausa" checkbox in the employee availability forms.';
