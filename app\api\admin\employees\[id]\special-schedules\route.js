import { NextResponse } from 'next/server';
import { authenticateAdmin } from '../../../../../../lib/supabaseAuthUtils.js';
import { 
  setEmployeeSpecialSchedule,
  deleteEmployeeSpecialSchedule
} from '../../../../../../lib/supabaseAvailabilityUtils.js';

/**
 * POST /api/admin/employees/[id]/special-schedules
 * Create or update employee special schedule
 */
export async function POST(request, { params }) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const { id: employeeId } = params;
    const scheduleData = await request.json();

    if (!employeeId) {
      return NextResponse.json(
        { success: false, message: 'ID dipendente obbligatorio' },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!scheduleData.date) {
      return NextResponse.json(
        { success: false, message: 'Data obbligatoria' },
        { status: 400 }
      );
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(scheduleData.date)) {
      return NextResponse.json(
        { success: false, message: 'Formato data non valido (usa YYYY-MM-DD)' },
        { status: 400 }
      );
    }

    // If available, validate time fields
    if (scheduleData.is_available) {
      if (!scheduleData.start_time || !scheduleData.end_time) {
        return NextResponse.json(
          { success: false, message: 'Orari di inizio e fine obbligatori quando disponibile' },
          { status: 400 }
        );
      }

      // Validate time format
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!timeRegex.test(scheduleData.start_time) || !timeRegex.test(scheduleData.end_time)) {
        return NextResponse.json(
          { success: false, message: 'Formato orario non valido (usa HH:MM)' },
          { status: 400 }
        );
      }

      // Validate that end time is after start time
      if (scheduleData.end_time <= scheduleData.start_time) {
        return NextResponse.json(
          { success: false, message: 'L\'orario di fine deve essere successivo all\'orario di inizio' },
          { status: 400 }
        );
      }

      // Validate break times if provided and break is not disabled
      if (!scheduleData.break_disabled && (scheduleData.break_start_time || scheduleData.break_end_time)) {
        if (scheduleData.break_start_time && scheduleData.break_end_time) {
          if (!timeRegex.test(scheduleData.break_start_time) || !timeRegex.test(scheduleData.break_end_time)) {
            return NextResponse.json(
              { success: false, message: 'Formato orario pausa non valido (usa HH:MM)' },
              { status: 400 }
            );
          }

          if (scheduleData.break_end_time <= scheduleData.break_start_time) {
            return NextResponse.json(
              { success: false, message: 'L\'orario di fine pausa deve essere successivo all\'orario di inizio pausa' },
              { status: 400 }
            );
          }

          if (scheduleData.break_start_time < scheduleData.start_time || scheduleData.break_end_time > scheduleData.end_time) {
            return NextResponse.json(
              { success: false, message: 'Gli orari di pausa devono essere compresi nell\'orario di lavoro' },
              { status: 400 }
            );
          }
        } else {
          return NextResponse.json(
            { success: false, message: 'Specificare sia l\'orario di inizio che di fine pausa' },
            { status: 400 }
          );
        }
      }
    }

    // Create/update special schedule
    const specialSchedule = await setEmployeeSpecialSchedule({
      employee_id: employeeId,
      ...scheduleData
    });

    return NextResponse.json({
      success: true,
      message: 'Programma speciale salvato con successo',
      data: specialSchedule
    });

  } catch (error) {
    console.error('Error creating/updating special schedule:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Errore interno del server',
        error: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/employees/[id]/special-schedules
 * Delete employee special schedule
 */
export async function DELETE(request, { params }) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const { id: employeeId } = params;
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    if (!employeeId) {
      return NextResponse.json(
        { success: false, message: 'ID dipendente obbligatorio' },
        { status: 400 }
      );
    }

    if (!date) {
      return NextResponse.json(
        { success: false, message: 'Data obbligatoria' },
        { status: 400 }
      );
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date)) {
      return NextResponse.json(
        { success: false, message: 'Formato data non valido (usa YYYY-MM-DD)' },
        { status: 400 }
      );
    }

    // Delete special schedule
    await deleteEmployeeSpecialSchedule(employeeId, date);

    return NextResponse.json({
      success: true,
      message: 'Programma speciale eliminato con successo'
    });

  } catch (error) {
    console.error('Error deleting special schedule:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Errore interno del server',
        error: error.message 
      },
      { status: 500 }
    );
  }
}
