# Employee Management System - Implementation Guide

## Overview

This guide documents the complete implementation of the Employee Management System for the CAF Appointment Booking System. The system replaces hardcoded employee data with a dynamic, database-driven solution that includes full CRUD operations.

## 🎯 Features Implemented

### 1. Database Schema
- **New `employees` table** with comprehensive fields
- **Foreign key relationship** with appointments table
- **Row Level Security (RLS)** policies for data protection
- **Indexes** for optimal performance
- **Triggers** for automatic timestamp updates

### 2. Admin Panel Integration
- **New "Gestione Dipendenti" tab** in admin panel
- **Employee list view** with filtering and search
- **Create/Edit employee modal** with form validation
- **Delete functionality** with safeguards
- **Real-time updates** and error handling

### 3. Dynamic Employee Selection
- **Updated appointment forms** (both user and admin)
- **API-driven employee loading** with fallback
- **Loading states** and error handling
- **Backward compatibility** maintained

### 4. API Endpoints
- **Public API** for active employees (`/api/employees`)
- **Admin CRUD APIs** for employee management
- **Proper authentication** and validation
- **Error handling** and status codes

## 📁 Files Created/Modified

### New Files Created:
```
supabase-employees-migration.sql     # Database migration script
lib/supabaseEmployeeUtils.js         # Employee utility functions
app/api/admin/employees/route.js     # Admin employee CRUD API
app/api/admin/employees/[id]/route.js # Individual employee operations
app/api/employees/route.js           # Public employee API
components/EmployeeList.js           # Employee list component
components/EmployeeForm.js           # Employee create/edit form
components/EmployeeManagement.js     # Main employee management component
scripts/test-employee-system.js      # Testing script
EMPLOYEE_MANAGEMENT_GUIDE.md         # This documentation
```

### Files Modified:
```
lib/supabase.js                      # Added EMPLOYEES table constant
app/admin/page.js                    # Added employee management tab
components/AppointmentForm.js        # Dynamic employee selection
components/AdminBookingForm.js       # Dynamic employee selection
```

## 🗄️ Database Schema

### Employees Table Structure:
```sql
employees (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE,
  role VARCHAR(100),
  department VARCHAR(100),
  phone VARCHAR(20),
  is_active BOOLEAN DEFAULT true,
  specializations TEXT[],
  working_hours JSONB,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
```

### Relationship with Appointments:
```sql
-- Added to appointments table
employee_id UUID REFERENCES employees(id)
```

## 🚀 Installation & Setup

### Step 1: Run Database Migration
```bash
# In your Supabase SQL Editor, run:
supabase-employees-migration.sql
```

This will:
- ✅ Create the employees table
- ✅ Set up indexes and constraints
- ✅ Configure Row Level Security
- ✅ Insert default employees
- ✅ Add employee_id to appointments table

### Step 2: Test the System
```bash
# Test employee management functionality
npm run test-employee-system

# Or manually:
node scripts/test-employee-system.js
```

### Step 3: Start Development Server
```bash
npm run dev
```

### Step 4: Access Employee Management
1. Go to `http://localhost:3000/admin`
2. Login with admin credentials
3. Click on "Gestione Dipendenti" tab
4. Start managing employees!

## 🎮 Usage Guide

### Admin Panel - Employee Management

#### Creating a New Employee:
1. Click "Nuovo Dipendente" button
2. Fill in employee details:
   - **Name** (required)
   - **Email** (optional, must be unique)
   - **Role** (e.g., "Operatore CAF")
   - **Department** (select from dropdown)
   - **Phone** (optional)
   - **Specializations** (check applicable services)
   - **Active Status** (checkbox)
   - **Notes** (optional)
3. Click "Crea" to save

#### Editing an Employee:
1. Click "Modifica" button on employee card
2. Update desired fields
3. Click "Aggiorna" to save changes

#### Deleting an Employee:
1. Click "Elimina" button on employee card
2. Confirm deletion in popup
3. **Note**: If employee has appointments, they will be deactivated instead of deleted

#### Filtering and Search:
- **Filter tabs**: All, Active, Inactive
- **Search box**: Search by name, email, role, or department

### Appointment Booking

The employee selection now works dynamically:
- **User form**: Loads active employees from database
- **Admin form**: Same dynamic loading
- **Fallback**: If API fails, uses hardcoded defaults
- **Loading state**: Shows "Caricamento operatori..." while loading

## 🔧 API Reference

### Public Endpoints

#### GET /api/employees
Get active employees for appointment booking.

**Query Parameters:**
- `specialization` (optional): Filter by specialization

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "Employee Name",
      "role": "Role",
      "department": "Department",
      "specializations": ["Service1", "Service2"]
    }
  ]
}
```

### Admin Endpoints (Require Authentication)

#### GET /api/admin/employees
Get all employees or statistics.

**Query Parameters:**
- `active=true`: Get only active employees
- `stats=true`: Get employee statistics

#### POST /api/admin/employees
Create a new employee.

**Request Body:**
```json
{
  "name": "Employee Name",
  "email": "<EMAIL>",
  "role": "Role",
  "department": "Department",
  "phone": "123456789",
  "is_active": true,
  "specializations": ["Service1", "Service2"],
  "notes": "Notes"
}
```

#### GET /api/admin/employees/[id]
Get specific employee by ID.

#### PUT /api/admin/employees/[id]
Update specific employee.

#### DELETE /api/admin/employees/[id]
Delete specific employee (soft delete if has appointments).

## 🛡️ Security Features

### Row Level Security (RLS)
- **Public read access** for active employees (appointment booking)
- **Admin full access** through API authentication
- **Data isolation** between different access levels

### Data Validation
- **Required fields** validation
- **Email format** validation
- **Unique email** constraint
- **Prevent deletion** of "Qualsiasi" employee

### Error Handling
- **Graceful fallbacks** if API fails
- **User-friendly error messages**
- **Proper HTTP status codes**
- **Console logging** for debugging

## 🧪 Testing

### Automated Tests
Run the test script to verify all functionality:
```bash
node scripts/test-employee-system.js
```

Tests include:
- ✅ CRUD operations
- ✅ Data validation
- ✅ Duplicate prevention
- ✅ API endpoints
- ✅ Integration with appointments

### Manual Testing Checklist

#### Admin Panel:
- [ ] Can access "Gestione Dipendenti" tab
- [ ] Can create new employee
- [ ] Can edit existing employee
- [ ] Can delete employee (with confirmation)
- [ ] Can filter employees (All/Active/Inactive)
- [ ] Can search employees
- [ ] Form validation works correctly

#### Appointment Forms:
- [ ] Employee dropdown loads dynamically
- [ ] Shows loading state while fetching
- [ ] Falls back to defaults if API fails
- [ ] Can select employees in user form
- [ ] Can select employees in admin form

#### API Endpoints:
- [ ] Public employee API works
- [ ] Admin employee APIs require authentication
- [ ] Proper error responses
- [ ] Data validation works

## 🔄 Migration from Hardcoded System

The migration is designed to be seamless:

1. **Database migration** populates employees table with existing hardcoded employees
2. **Backward compatibility** maintained with fallback to hardcoded values
3. **Gradual transition** - old appointments still work
4. **No data loss** - all existing functionality preserved

### Default Employees Migrated:
- Qualsiasi (General - All Services)
- Antonello (CAF Services, Patronato)
- Federica (Legal Services, Immigration)
- Giuseppe (Patronato, Loans)
- Silvia (Medical Services)
- Tania (CAF Services, Patronato, Immigration)

## 🚨 Troubleshooting

### Common Issues:

#### Employee dropdown shows "Caricamento operatori..."
- Check if development server is running
- Verify API endpoint `/api/employees` is accessible
- Check browser console for errors

#### Cannot create employee with email
- Verify email format is valid
- Check if email already exists in database
- Ensure database connection is working

#### Employee management tab not visible
- Verify admin authentication
- Check if EmployeeManagement component is imported
- Ensure admin panel code is updated

#### Database errors
- Run migration script in Supabase SQL Editor
- Check Supabase connection
- Verify RLS policies are set correctly

### Debug Commands:
```bash
# Test Supabase connection
npm run test-supabase

# Test employee system
node scripts/test-employee-system.js

# Check server logs
npm run dev
```

## 🎉 Success Criteria

The employee management system is successfully implemented when:

- ✅ Admin can manage employees through the web interface
- ✅ Appointment forms load employees dynamically
- ✅ Database stores employee data properly
- ✅ API endpoints work correctly
- ✅ Security policies are in place
- ✅ Tests pass successfully
- ✅ Backward compatibility is maintained

## 📞 Support

For issues or questions:
1. Check this documentation
2. Run the test script
3. Check browser console for errors
4. Verify database migration was successful
5. Ensure all files are properly created/modified
