/**
 * Test Script for Appointment Management Fixes
 * This script tests the fixed appointment CRUD operations
 *
 * Usage: node scripts/test-appointment-fixes.js
 */

// Load environment variables first
import './load-env.js';

import { 
  readAppointments, 
  addAppointment, 
  updateAppointmentStatus,
  deleteAppointment,
  isTimeSlotAvailable,
  getDashboardStats 
} from '../lib/supabaseAppointmentUtils.js';

import { APPOINTMENT_STATUS } from '../lib/supabase.js';

console.log('🧪 Testing Appointment Management Fixes...\n');

async function testAppointmentCRUD() {
  try {
    // Test 1: Read appointments
    console.log('📋 Test 1: Reading appointments...');
    const appointments = await readAppointments();
    console.log(`✅ Found ${appointments.length} appointments`);
    
    if (appointments.length > 0) {
      const sampleAppointment = appointments[0];
      console.log(`   Sample: ${sampleAppointment.nome} ${sampleAppointment.cognome} - ${sampleAppointment.data_appuntamento} ${sampleAppointment.orario}`);
    }

    // Test 2: Create a test appointment
    console.log('\n➕ Test 2: Creating a test appointment...');
    const testAppointmentData = {
      nome: 'Test',
      cognome: 'Appointment',
      telefono: '1234567890',
      email: '<EMAIL>',
      servizio: 'Servizi CAF',
      prestazione: '',
      operatore: 'Antonello',
      noteAggiuntive: 'Test appointment created by test script',
      dataAppuntamento: '2025-01-15', // Future date
      orario: '10:00'
    };

    // Check if time slot is available first
    const isAvailable = await isTimeSlotAvailable(testAppointmentData.dataAppuntamento, testAppointmentData.orario);
    if (!isAvailable) {
      testAppointmentData.orario = '11:00'; // Try different time
      console.log('   Trying different time slot: 11:00');
    }

    const newAppointment = await addAppointment(testAppointmentData);
    console.log(`✅ Created appointment: ID ${newAppointment.id}`);
    console.log(`   Employee ID linked: ${newAppointment.employee_id || 'None'}`);

    // Test 3: Update appointment status
    console.log('\n✏️ Test 3: Updating appointment status...');
    const updatedAppointment = await updateAppointmentStatus(newAppointment.id, APPOINTMENT_STATUS.COMPLETED);
    console.log(`✅ Updated appointment status to: ${updatedAppointment.status}`);

    // Test 4: Update status again
    console.log('\n✏️ Test 4: Updating appointment status again...');
    const updatedAppointment2 = await updateAppointmentStatus(newAppointment.id, APPOINTMENT_STATUS.CONFIRMED);
    console.log(`✅ Updated appointment status to: ${updatedAppointment2.status}`);

    // Test 5: Delete the test appointment
    console.log('\n🗑️ Test 5: Deleting test appointment...');
    const deletedAppointment = await deleteAppointment(newAppointment.id);
    console.log(`✅ Deleted appointment: ${deletedAppointment.nome} ${deletedAppointment.cognome}`);

    // Test 6: Try to update deleted appointment (should fail)
    console.log('\n🚫 Test 6: Trying to update deleted appointment (should fail)...');
    try {
      await updateAppointmentStatus(newAppointment.id, APPOINTMENT_STATUS.COMPLETED);
      console.log('❌ Should have failed - appointment was deleted');
    } catch (error) {
      if (error.message.includes('No appointment found')) {
        console.log('✅ Correctly failed with "No appointment found" error');
      } else {
        console.log(`❌ Failed with unexpected error: ${error.message}`);
      }
    }

    // Test 7: Try to delete non-existent appointment (should fail)
    console.log('\n🚫 Test 7: Trying to delete non-existent appointment (should fail)...');
    try {
      await deleteAppointment(99999);
      console.log('❌ Should have failed - appointment does not exist');
    } catch (error) {
      if (error.message.includes('No appointment found')) {
        console.log('✅ Correctly failed with "No appointment found" error');
      } else {
        console.log(`❌ Failed with unexpected error: ${error.message}`);
      }
    }

    console.log('\n🎉 All appointment CRUD tests passed!');

  } catch (error) {
    console.error('❌ Appointment CRUD test failed:', error);
    console.error('Stack trace:', error.stack);
    throw error;
  }
}

async function testAPIEndpoints() {
  console.log('\n🌐 Testing API endpoints...');
  
  try {
    // Test admin appointments API
    console.log('📡 Testing admin appointments API...');
    
    // Note: These tests require the server to be running
    const baseUrl = 'http://localhost:3000';
    
    try {
      // Test GET appointments (without auth - should fail)
      const response = await fetch(`${baseUrl}/api/admin/appointments`);
      if (response.status === 401) {
        console.log('✅ Admin API correctly requires authentication');
      } else {
        console.log('⚠️ Admin API authentication may not be working properly');
      }
    } catch (error) {
      console.log('⚠️ Could not test API endpoints (server may not be running)');
    }

    // Test public employees API
    try {
      const response = await fetch(`${baseUrl}/api/employees`);
      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Public employees API working - found ${result.data?.length || 0} employees`);
      } else {
        console.log('⚠️ Public employees API not working properly');
      }
    } catch (error) {
      console.log('⚠️ Could not test public employees API (server may not be running)');
    }

  } catch (error) {
    console.error('❌ API endpoint test failed:', error);
  }
}

async function testDashboardStats() {
  console.log('\n📊 Testing dashboard statistics...');
  
  try {
    const stats = await getDashboardStats();
    console.log('✅ Dashboard stats retrieved:');
    console.log(`   - Total appointments: ${stats.total}`);
    console.log(`   - Today's appointments: ${stats.today}`);
    console.log(`   - This week's appointments: ${stats.thisWeek}`);
    console.log(`   - This month's appointments: ${stats.thisMonth}`);
    console.log(`   - Status distribution:`, stats.statusCounts);
  } catch (error) {
    console.error('❌ Dashboard stats test failed:', error);
  }
}

async function testEmployeeIntegration() {
  console.log('\n👥 Testing employee system integration...');
  
  try {
    // Import employee utilities
    const { getEmployees } = await import('../lib/supabaseEmployeeUtils.js');
    
    const employees = await getEmployees(true); // Get active employees
    console.log(`✅ Found ${employees.length} active employees`);
    
    if (employees.length > 0) {
      console.log('   Available employees:');
      employees.forEach(emp => {
        console.log(`     - ${emp.name} (${emp.role || 'No role'}) - ${emp.department || 'No department'}`);
      });
    }
    
    console.log('✅ Employee system integration working');
  } catch (error) {
    console.error('❌ Employee integration test failed:', error);
  }
}

// Run all tests
async function runAllTests() {
  try {
    await testAppointmentCRUD();
    await testDashboardStats();
    await testEmployeeIntegration();
    await testAPIEndpoints();
    
    console.log('\n📋 Summary:');
    console.log('✅ Appointment CRUD operations fixed and tested');
    console.log('✅ Error handling improved');
    console.log('✅ Employee system integration verified');
    console.log('✅ Dashboard statistics working');
    console.log('\n🚀 All appointment management fixes are working correctly!');
    
  } catch (error) {
    console.error('\n❌ Some tests failed. Please check the errors above.');
    process.exit(1);
  }
}

runAllTests().catch(console.error);
