'use client';

import { useState } from 'react';
import EmployeeList from './EmployeeList';
import EmployeeForm from './EmployeeForm';

export default function EmployeeManagement({ token }) {
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleCreateEmployee = () => {
    setSelectedEmployee(null);
    setIsFormOpen(true);
  };

  const handleEditEmployee = (employee) => {
    setSelectedEmployee(employee);
    setIsFormOpen(true);
  };

  const handleDeleteEmployee = (employee) => {
    // Refresh the list after deletion
    setRefreshTrigger(prev => prev + 1);
  };

  const handleFormSave = (savedEmployee) => {
    // Refresh the list after save
    setRefreshTrigger(prev => prev + 1);
    
    // Close form after successful save
    setTimeout(() => {
      setIsFormOpen(false);
      setSelectedEmployee(null);
    }, 1500);
  };

  const handleFormCancel = () => {
    setIsFormOpen(false);
    setSelectedEmployee(null);
  };

  return (
    <div className="space-y-6">
      <EmployeeList
        token={token}
        onEditEmployee={handleEditEmployee}
        onDeleteEmployee={handleDeleteEmployee}
        onCreateEmployee={handleCreateEmployee}
        refreshTrigger={refreshTrigger}
      />

      <EmployeeForm
        employee={selectedEmployee}
        token={token}
        onSave={handleFormSave}
        onCancel={handleFormCancel}
        isOpen={isFormOpen}
      />
    </div>
  );
}
