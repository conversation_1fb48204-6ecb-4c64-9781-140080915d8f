/**
 * Comprehensive Diagnosis Script for Appointment Issues
 * This script will identify the exact cause of appointment management problems
 *
 * Usage: node scripts/diagnose-appointment-issues.js
 */

// Load environment variables first
import './load-env.js';

import { supabase, supabaseAdmin, TABLES } from '../lib/supabase.js';

console.log('🔍 Comprehensive Appointment Issues Diagnosis...\n');

async function checkSupabaseConnection() {
  console.log('1️⃣ Testing Supabase Connection...');
  
  try {
    // Test regular client
    const { data: testData, error: testError } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('count(*)')
      .limit(1);
    
    if (testError) {
      console.error('❌ Regular Supabase client error:', testError);
      return false;
    } else {
      console.log('✅ Regular Supabase client working');
    }
    
    // Test admin client
    const { data: adminData, error: adminError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .select('count(*)')
      .limit(1);
    
    if (adminError) {
      console.error('❌ Admin Supabase client error:', adminError);
      return false;
    } else {
      console.log('✅ Admin Supabase client working');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Supabase connection test failed:', error);
    return false;
  }
}

async function checkAppointmentData() {
  console.log('\n2️⃣ Checking Appointment Data Structure...');
  
  try {
    const { data: appointments, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .limit(5);
    
    if (error) {
      console.error('❌ Error fetching appointments:', error);
      return null;
    }
    
    if (appointments.length === 0) {
      console.log('⚠️ No appointments found in database');
      return [];
    }
    
    console.log(`✅ Found ${appointments.length} appointments`);
    console.log('Sample appointment structure:');
    
    const sample = appointments[0];
    console.log('Fields:', Object.keys(sample));
    console.log('ID type:', typeof sample.id, 'Value:', sample.id);
    console.log('Status:', sample.status);
    console.log('Employee ID:', sample.employee_id);
    
    return appointments;
  } catch (error) {
    console.error('❌ Error checking appointment data:', error);
    return null;
  }
}

async function testDirectDatabaseOperations(appointments) {
  console.log('\n3️⃣ Testing Direct Database Operations...');
  
  if (!appointments || appointments.length === 0) {
    console.log('⚠️ No appointments to test with');
    return;
  }
  
  const testAppointment = appointments[0];
  const appointmentId = testAppointment.id;
  
  console.log(`Testing with appointment ID: ${appointmentId} (type: ${typeof appointmentId})`);
  
  // Test 1: Direct select by ID
  console.log('\n🔍 Test 1: Direct select by ID...');
  try {
    const { data: selectData, error: selectError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .eq('id', appointmentId)
      .single();
    
    if (selectError) {
      console.error('❌ Direct select failed:', selectError);
    } else {
      console.log('✅ Direct select successful');
    }
  } catch (error) {
    console.error('❌ Direct select exception:', error);
  }
  
  // Test 2: Direct update
  console.log('\n🔍 Test 2: Direct update...');
  try {
    const originalStatus = testAppointment.status;
    const newStatus = originalStatus === 'confirmed' ? 'completed' : 'confirmed';
    
    const { data: updateData, error: updateError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .update({ status: newStatus })
      .eq('id', appointmentId)
      .select();
    
    if (updateError) {
      console.error('❌ Direct update failed:', updateError);
    } else {
      console.log('✅ Direct update successful:', updateData);
      
      // Revert the change
      await supabaseAdmin
        .from(TABLES.APPOINTMENTS)
        .update({ status: originalStatus })
        .eq('id', appointmentId);
    }
  } catch (error) {
    console.error('❌ Direct update exception:', error);
  }
  
  // Test 3: Test with string conversion
  console.log('\n🔍 Test 3: Testing with string/number conversion...');
  try {
    const stringId = String(appointmentId);
    const numericId = typeof appointmentId === 'string' ? parseInt(appointmentId, 10) : appointmentId;
    
    console.log(`Original ID: ${appointmentId} (${typeof appointmentId})`);
    console.log(`String ID: ${stringId} (${typeof stringId})`);
    console.log(`Numeric ID: ${numericId} (${typeof numericId})`);
    
    // Test with numeric ID
    const { data: numericData, error: numericError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .select('id')
      .eq('id', numericId)
      .single();
    
    if (numericError) {
      console.error('❌ Numeric ID query failed:', numericError);
    } else {
      console.log('✅ Numeric ID query successful');
    }
    
    // Test with string ID
    const { data: stringData, error: stringError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .select('id')
      .eq('id', stringId)
      .single();
    
    if (stringError) {
      console.error('❌ String ID query failed:', stringError);
    } else {
      console.log('✅ String ID query successful');
    }
    
  } catch (error) {
    console.error('❌ ID conversion test exception:', error);
  }
}

async function testAuthenticationFlow() {
  console.log('\n4️⃣ Testing Authentication Flow...');
  
  try {
    const { validateAdminCredentials } = await import('../lib/supabaseAuthUtils.js');
    
    const adminUser = await validateAdminCredentials('admin', 'caf2024!');
    
    if (adminUser && adminUser.token) {
      console.log('✅ Admin authentication successful');
      console.log('Token length:', adminUser.token.length);
      
      // Test API call with token
      try {
        const response = await fetch('http://localhost:3000/api/admin/appointments', {
          headers: {
            'Authorization': `Bearer ${adminUser.token}`
          }
        });
        
        console.log(`API response status: ${response.status}`);
        
        if (response.ok) {
          console.log('✅ API authentication working');
        } else {
          const errorText = await response.text();
          console.log('❌ API authentication failed:', errorText);
        }
      } catch (fetchError) {
        console.log('⚠️ Could not test API (server may not be running)');
      }
    } else {
      console.log('❌ Admin authentication failed');
    }
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
  }
}

async function testEmployeeSystemIntegration() {
  console.log('\n5️⃣ Testing Employee System Integration...');
  
  try {
    // Check if employees table exists and has data
    const { data: employees, error: empError } = await supabase
      .from(TABLES.EMPLOYEES)
      .select('*')
      .limit(5);
    
    if (empError) {
      console.error('❌ Employee table error:', empError);
      console.log('⚠️ Employee system may not be properly migrated');
    } else {
      console.log(`✅ Employee table accessible - found ${employees.length} employees`);
      
      if (employees.length > 0) {
        console.log('Sample employee:', employees[0].name);
      }
    }
    
    // Check if appointments have employee_id column
    const { data: appointmentSample, error: aptError } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('id, employee_id, operatore')
      .limit(1);
    
    if (aptError) {
      console.error('❌ Error checking appointment employee fields:', aptError);
    } else if (appointmentSample.length > 0) {
      const sample = appointmentSample[0];
      console.log('✅ Appointment employee fields:');
      console.log(`  - employee_id: ${sample.employee_id}`);
      console.log(`  - operatore: ${sample.operatore}`);
    }
    
  } catch (error) {
    console.error('❌ Employee system integration test failed:', error);
  }
}

async function checkRLSPolicies() {
  console.log('\n6️⃣ Checking Row Level Security Policies...');
  
  try {
    // Test with regular client (should work for reads)
    const { data: publicData, error: publicError } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('id')
      .limit(1);
    
    if (publicError) {
      console.error('❌ Public read access failed:', publicError);
    } else {
      console.log('✅ Public read access working');
    }
    
    // Test with admin client (should work for all operations)
    const { data: adminData, error: adminError } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .select('id')
      .limit(1);
    
    if (adminError) {
      console.error('❌ Admin access failed:', adminError);
    } else {
      console.log('✅ Admin access working');
    }
    
  } catch (error) {
    console.error('❌ RLS policy test failed:', error);
  }
}

async function generateDiagnosisReport() {
  console.log('\n📋 DIAGNOSIS REPORT');
  console.log('==================');
  
  const connectionOk = await checkSupabaseConnection();
  const appointments = await checkAppointmentData();
  
  if (connectionOk && appointments) {
    await testDirectDatabaseOperations(appointments);
  }
  
  await testAuthenticationFlow();
  await testEmployeeSystemIntegration();
  await checkRLSPolicies();
  
  console.log('\n🎯 RECOMMENDATIONS:');
  console.log('1. Check the output above for any ❌ errors');
  console.log('2. Verify that the employee migration was run successfully');
  console.log('3. Ensure the development server is running for API tests');
  console.log('4. Check browser console for frontend errors');
  console.log('5. Verify admin credentials are correct');
  
  console.log('\n📞 If issues persist:');
  console.log('- Run: node scripts/debug-appointment-api.js');
  console.log('- Check server logs when performing operations');
  console.log('- Verify Supabase dashboard for RLS policy issues');
}

generateDiagnosisReport().catch(console.error);
