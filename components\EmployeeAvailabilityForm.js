'use client';

import { useState, useEffect } from 'react';

const DAYS_OF_WEEK = [
  { value: 1, label: 'Luned<PERSON>', shortLabel: 'Lun' },
  { value: 2, label: '<PERSON><PERSON><PERSON>', shortLabel: 'Mar' },
  { value: 3, label: '<PERSON><PERSON><PERSON><PERSON><PERSON>', shortLabel: 'Me<PERSON>' },
  { value: 4, label: '<PERSON><PERSON><PERSON><PERSON>', shortLabel: '<PERSON><PERSON>' },
  { value: 5, label: 'V<PERSON><PERSON><PERSON>', shortLabel: 'Ven' },
  { value: 6, label: '<PERSON>bat<PERSON>', shortLabel: 'Sab' },
  { value: 0, label: 'Dome<PERSON>', shortLabel: 'Dom' }
];

const DEFAULT_AVAILABILITY = {
  is_available: true,
  start_time: '09:00',
  end_time: '18:00',
  break_start_time: '12:00',
  break_end_time: '15:00',
  break_disabled: false,
  notes: ''
};

export default function EmployeeAvailabilityForm({ 
  employeeId, 
  initialAvailability = [], 
  onSave, 
  onCancel,
  isSubmitting = false 
}) {
  const [availability, setAvailability] = useState({});
  const [errors, setErrors] = useState({});

  // Initialize availability data
  useEffect(() => {
    const availabilityMap = {};
    
    // Initialize with default values for all days
    DAYS_OF_WEEK.forEach(day => {
      availabilityMap[day.value] = { ...DEFAULT_AVAILABILITY, is_available: false };
    });

    // Override with existing availability data
    initialAvailability.forEach(avail => {
      availabilityMap[avail.day_of_week] = {
        is_available: avail.is_available,
        start_time: avail.start_time || '09:00',
        end_time: avail.end_time || '18:00',
        break_start_time: avail.break_start_time || '',
        break_end_time: avail.break_end_time || '',
        break_disabled: avail.break_disabled || false,
        notes: avail.notes || ''
      };
    });

    setAvailability(availabilityMap);
  }, [initialAvailability]);

  const validateTimeRange = (startTime, endTime, breakStartTime = '', breakEndTime = '', breakDisabled = false) => {
    const errors = [];

    if (endTime <= startTime) {
      errors.push('L\'orario di fine deve essere successivo all\'orario di inizio');
    }

    // Only validate break times if break is not disabled
    if (!breakDisabled) {
      if (breakStartTime && breakEndTime) {
        if (breakEndTime <= breakStartTime) {
          errors.push('L\'orario di fine pausa deve essere successivo all\'orario di inizio pausa');
        }

        if (breakStartTime < startTime || breakEndTime > endTime) {
          errors.push('Gli orari di pausa devono essere compresi nell\'orario di lavoro');
        }
      } else if (breakStartTime || breakEndTime) {
        errors.push('Specificare sia l\'orario di inizio che di fine pausa');
      }
    }

    return errors;
  };

  const handleAvailabilityChange = (dayOfWeek, field, value) => {
    setAvailability(prev => ({
      ...prev,
      [dayOfWeek]: {
        ...prev[dayOfWeek],
        [field]: value
      }
    }));

    // Clear errors for this day when user makes changes
    if (errors[dayOfWeek]) {
      setErrors(prev => ({
        ...prev,
        [dayOfWeek]: []
      }));
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    const newErrors = {};
    const availabilityData = [];

    // Validate and prepare data
    Object.entries(availability).forEach(([dayOfWeek, dayAvail]) => {
      if (dayAvail.is_available) {
        const dayErrors = validateTimeRange(
          dayAvail.start_time,
          dayAvail.end_time,
          dayAvail.break_start_time,
          dayAvail.break_end_time,
          dayAvail.break_disabled
        );

        if (dayErrors.length > 0) {
          newErrors[dayOfWeek] = dayErrors;
        } else {
          availabilityData.push({
            day_of_week: parseInt(dayOfWeek),
            is_available: true,
            start_time: dayAvail.start_time,
            end_time: dayAvail.end_time,
            break_start_time: dayAvail.break_disabled ? null : (dayAvail.break_start_time || null),
            break_end_time: dayAvail.break_disabled ? null : (dayAvail.break_end_time || null),
            break_disabled: dayAvail.break_disabled || false,
            notes: dayAvail.notes || null
          });
        }
      }
    });

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      onSave(availabilityData);
    }
  };

  const copyToAllDays = (sourceDay) => {
    const sourceAvailability = availability[sourceDay];
    if (!sourceAvailability) return;

    const newAvailability = { ...availability };
    DAYS_OF_WEEK.forEach(day => {
      if (day.value !== sourceDay) {
        newAvailability[day.value] = { ...sourceAvailability };
      }
    });

    setAvailability(newAvailability);
    setErrors({});
  };

  const setWorkingDaysOnly = () => {
    const newAvailability = { ...availability };
    
    // Set Monday-Friday as available with default hours
    [1, 2, 3, 4, 5].forEach(day => {
      newAvailability[day] = { ...DEFAULT_AVAILABILITY };
    });

    // Set Saturday-Sunday as unavailable
    [0, 6].forEach(day => {
      newAvailability[day] = { ...DEFAULT_AVAILABILITY, is_available: false };
    });

    setAvailability(newAvailability);
    setErrors({});
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">
          Orari di Disponibilità
        </h3>
        <div className="flex space-x-2">
          <button
            type="button"
            onClick={setWorkingDaysOnly}
            className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
          >
            Lun-Ven Standard
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {DAYS_OF_WEEK.map(day => {
          const dayAvail = availability[day.value] || DEFAULT_AVAILABILITY;
          const dayErrors = errors[day.value] || [];

          return (
            <div key={day.value} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id={`available-${day.value}`}
                    checked={dayAvail.is_available}
                    onChange={(e) => handleAvailabilityChange(day.value, 'is_available', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor={`available-${day.value}`} className="font-medium text-gray-900">
                    {day.label}
                  </label>
                </div>
                
                {dayAvail.is_available && (
                  <button
                    type="button"
                    onClick={() => copyToAllDays(day.value)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Copia a tutti i giorni
                  </button>
                )}
              </div>

              {dayAvail.is_available && (
                <div className="space-y-4">
                  {/* Orari di lavoro */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Inizio
                      </label>
                      <input
                        type="time"
                        value={dayAvail.start_time}
                        onChange={(e) => handleAvailabilityChange(day.value, 'start_time', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Fine
                      </label>
                      <input
                        type="time"
                        value={dayAvail.end_time}
                        onChange={(e) => handleAvailabilityChange(day.value, 'end_time', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  {/* Gestione pausa */}
                  <div className="border-t pt-3">
                    <div className="flex items-center space-x-3 mb-3">
                      <input
                        type="checkbox"
                        id={`break-disabled-${day.value}`}
                        checked={dayAvail.break_disabled}
                        onChange={(e) => {
                          handleAvailabilityChange(day.value, 'break_disabled', e.target.checked);
                          // Clear break times when disabling break
                          if (e.target.checked) {
                            handleAvailabilityChange(day.value, 'break_start_time', '');
                            handleAvailabilityChange(day.value, 'break_end_time', '');
                          }
                        }}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor={`break-disabled-${day.value}`} className="text-sm font-medium text-gray-700">
                        Disabilita pausa (orario continuato)
                      </label>
                    </div>

                    {!dayAvail.break_disabled && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Pausa Inizio
                          </label>
                          <input
                            type="time"
                            value={dayAvail.break_start_time}
                            onChange={(e) => handleAvailabilityChange(day.value, 'break_start_time', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="es. 12:00"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Pausa Fine
                          </label>
                          <input
                            type="time"
                            value={dayAvail.break_end_time}
                            onChange={(e) => handleAvailabilityChange(day.value, 'break_end_time', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="es. 15:00"
                          />
                        </div>
                      </div>
                    )}

                    {dayAvail.break_disabled && (
                      <p className="text-sm text-gray-600 italic">
                        Pausa disabilitata - orario di lavoro continuato
                      </p>
                    )}
                  </div>
                </div>
              )}

              {dayAvail.is_available && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Note (opzionale)
                  </label>
                  <input
                    type="text"
                    value={dayAvail.notes}
                    onChange={(e) => handleAvailabilityChange(day.value, 'notes', e.target.value)}
                    placeholder="Note aggiuntive per questo giorno..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              )}

              {dayErrors.length > 0 && (
                <div className="text-red-600 text-sm space-y-1">
                  {dayErrors.map((error, index) => (
                    <p key={index}>{error}</p>
                  ))}
                </div>
              )}
            </div>
          );
        })}

        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={onCancel}
            disabled={isSubmitting}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50"
          >
            Annulla
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {isSubmitting ? 'Salvataggio...' : 'Salva Disponibilità'}
          </button>
        </div>
      </form>
    </div>
  );
}
