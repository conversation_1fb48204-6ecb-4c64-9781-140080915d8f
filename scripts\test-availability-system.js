/**
 * Test script for Employee Availability System
 * Run with: node scripts/test-availability-system.js
 */

import { supabaseAdmin } from '../lib/supabase.js';
import { 
  getEmployeeAvailability, 
  setEmployeeAvailability,
  isEmployeeAvailable,
  getAvailableEmployees,
  generateDefaultAvailability,
  validateAvailabilityTimeRange
} from '../lib/supabaseAvailabilityUtils.js';

// Test configuration
const TEST_CONFIG = {
  testEmployeeName: 'Test Employee Availability',
  testEmployeeEmail: '<EMAIL>'
};

let testEmployeeId = null;

/**
 * Create a test employee for availability testing
 */
async function createTestEmployee() {
  console.log('🔧 Creating test employee...');
  
  try {
    const { data, error } = await supabaseAdmin
      .from('employees')
      .insert({
        name: TEST_CONFIG.testEmployeeName,
        email: TEST_CONFIG.testEmployeeEmail,
        role: 'Test Role',
        department: 'Test Department',
        is_active: true,
        specializations: ['Servizi CAF', 'Patronato']
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    testEmployeeId = data.id;
    console.log(`✅ Test employee created with ID: ${testEmployeeId}`);
    return data;
  } catch (error) {
    console.error('❌ Error creating test employee:', error.message);
    throw error;
  }
}

/**
 * Test basic availability CRUD operations
 */
async function testAvailabilityCRUD() {
  console.log('\n📋 Testing Availability CRUD Operations...');

  try {
    // Test 1: Generate default availability
    console.log('  Testing default availability generation...');
    const defaultAvailability = generateDefaultAvailability();
    console.log(`  ✅ Generated ${defaultAvailability.length} default availability records`);

    // Test 2: Set employee availability
    console.log('  Testing set employee availability...');
    const setResult = await setEmployeeAvailability(testEmployeeId, defaultAvailability);
    console.log(`  ✅ Set availability: ${setResult.length} records created`);

    // Test 3: Get employee availability
    console.log('  Testing get employee availability...');
    const getResult = await getEmployeeAvailability(testEmployeeId);
    console.log(`  ✅ Retrieved availability: ${getResult.length} records found`);

    // Test 4: Validate availability data structure
    if (getResult.length > 0) {
      const firstRecord = getResult[0];
      const requiredFields = ['id', 'employee_id', 'day_of_week', 'start_time', 'end_time', 'is_available'];
      const hasAllFields = requiredFields.every(field => firstRecord.hasOwnProperty(field));
      
      if (hasAllFields) {
        console.log('  ✅ Availability record structure is valid');
      } else {
        console.log('  ❌ Availability record missing required fields');
      }
    }

    return true;
  } catch (error) {
    console.error('  ❌ CRUD test failed:', error.message);
    return false;
  }
}

/**
 * Test availability checking functions
 */
async function testAvailabilityChecking() {
  console.log('\n🔍 Testing Availability Checking...');

  try {
    const testDate = '2024-01-15'; // Monday
    const testTime = '10:00';
    const unavailableTime = '13:00'; // During lunch break

    // Test 1: Check if employee is available during working hours
    console.log('  Testing availability during working hours...');
    const isAvailableWorking = await isEmployeeAvailable(testEmployeeId, testDate, testTime);
    console.log(`  ✅ Employee available at ${testTime}: ${isAvailableWorking}`);

    // Test 2: Check if employee is unavailable during break
    console.log('  Testing availability during break time...');
    const isAvailableBreak = await isEmployeeAvailable(testEmployeeId, testDate, unavailableTime);
    console.log(`  ✅ Employee available at ${unavailableTime}: ${isAvailableBreak}`);

    // Test 3: Get available employees for a time slot
    console.log('  Testing get available employees...');
    const availableEmployees = await getAvailableEmployees(testDate, testTime);
    console.log(`  ✅ Found ${availableEmployees.length} available employees`);

    // Test 4: Check weekend availability (should be false)
    console.log('  Testing weekend availability...');
    const weekendDate = '2024-01-14'; // Sunday
    const isAvailableWeekend = await isEmployeeAvailable(testEmployeeId, weekendDate, testTime);
    console.log(`  ✅ Employee available on weekend: ${isAvailableWeekend}`);

    return true;
  } catch (error) {
    console.error('  ❌ Availability checking test failed:', error.message);
    return false;
  }
}

/**
 * Test validation functions
 */
async function testValidation() {
  console.log('\n✅ Testing Validation Functions...');

  try {
    // Test 1: Valid time range
    console.log('  Testing valid time range...');
    const validResult = validateAvailabilityTimeRange('09:00', '18:00', '12:00', '15:00');
    console.log(`  ✅ Valid time range validation: ${validResult.isValid}`);

    // Test 2: Invalid time range (end before start)
    console.log('  Testing invalid time range...');
    const invalidResult = validateAvailabilityTimeRange('18:00', '09:00');
    console.log(`  ✅ Invalid time range validation: ${!invalidResult.isValid} (should be false)`);

    // Test 3: Invalid break times
    console.log('  Testing invalid break times...');
    const invalidBreakResult = validateAvailabilityTimeRange('09:00', '18:00', '15:00', '12:00');
    console.log(`  ✅ Invalid break times validation: ${!invalidBreakResult.isValid} (should be false)`);

    // Test 4: Break times outside working hours
    console.log('  Testing break times outside working hours...');
    const outsideBreakResult = validateAvailabilityTimeRange('09:00', '18:00', '08:00', '10:00');
    console.log(`  ✅ Break outside working hours validation: ${!outsideBreakResult.isValid} (should be false)`);

    return true;
  } catch (error) {
    console.error('  ❌ Validation test failed:', error.message);
    return false;
  }
}

/**
 * Test edge cases and error handling
 */
async function testEdgeCases() {
  console.log('\n🚨 Testing Edge Cases...');

  try {
    // Test 1: Non-existent employee
    console.log('  Testing non-existent employee...');
    const fakeEmployeeId = '00000000-0000-0000-0000-000000000000';
    const fakeResult = await getEmployeeAvailability(fakeEmployeeId);
    console.log(`  ✅ Non-existent employee returns: ${fakeResult.length} records`);

    // Test 2: Invalid date format
    console.log('  Testing invalid date format...');
    try {
      await isEmployeeAvailable(testEmployeeId, 'invalid-date', '10:00');
      console.log('  ❌ Should have thrown error for invalid date');
    } catch (error) {
      console.log('  ✅ Correctly handled invalid date format');
    }

    // Test 3: Empty availability data
    console.log('  Testing empty availability data...');
    const emptyResult = await setEmployeeAvailability(testEmployeeId, []);
    console.log(`  ✅ Empty availability set: ${emptyResult.length} records`);

    // Test 4: Overlapping time slots
    console.log('  Testing overlapping time slots...');
    const overlappingAvailability = [
      {
        day_of_week: 1,
        start_time: '09:00',
        end_time: '13:00',
        is_available: true
      },
      {
        day_of_week: 1,
        start_time: '12:00',
        end_time: '18:00',
        is_available: true
      }
    ];

    try {
      await setEmployeeAvailability(testEmployeeId, overlappingAvailability);
      console.log('  ⚠️  Overlapping slots were allowed (check if this is intended)');
    } catch (error) {
      console.log('  ✅ Correctly prevented overlapping time slots');
    }

    return true;
  } catch (error) {
    console.error('  ❌ Edge cases test failed:', error.message);
    return false;
  }
}

/**
 * Clean up test data
 */
async function cleanup() {
  console.log('\n🧹 Cleaning up test data...');

  try {
    if (testEmployeeId) {
      // Delete availability records
      await supabaseAdmin
        .from('employee_availability')
        .delete()
        .eq('employee_id', testEmployeeId);

      // Delete special schedules
      await supabaseAdmin
        .from('employee_special_schedules')
        .delete()
        .eq('employee_id', testEmployeeId);

      // Delete test employee
      await supabaseAdmin
        .from('employees')
        .delete()
        .eq('id', testEmployeeId);

      console.log('✅ Test data cleaned up successfully');
    }
  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
  }
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 Starting Employee Availability System Tests\n');

  const results = {
    total: 0,
    passed: 0,
    failed: 0
  };

  try {
    // Setup
    await createTestEmployee();

    // Run tests
    const tests = [
      { name: 'CRUD Operations', fn: testAvailabilityCRUD },
      { name: 'Availability Checking', fn: testAvailabilityChecking },
      { name: 'Validation Functions', fn: testValidation },
      { name: 'Edge Cases', fn: testEdgeCases }
    ];

    for (const test of tests) {
      results.total++;
      try {
        const success = await test.fn();
        if (success) {
          results.passed++;
          console.log(`\n✅ ${test.name} - PASSED`);
        } else {
          results.failed++;
          console.log(`\n❌ ${test.name} - FAILED`);
        }
      } catch (error) {
        results.failed++;
        console.log(`\n❌ ${test.name} - ERROR: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Test setup failed:', error.message);
  } finally {
    // Cleanup
    await cleanup();
  }

  // Results summary
  console.log('\n' + '='.repeat(50));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('='.repeat(50));
  console.log(`Total Tests: ${results.total}`);
  console.log(`Passed: ${results.passed}`);
  console.log(`Failed: ${results.failed}`);
  console.log(`Success Rate: ${((results.passed / results.total) * 100).toFixed(1)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 All tests passed! The availability system is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the errors above.');
  }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(console.error);
}

export { runTests };
