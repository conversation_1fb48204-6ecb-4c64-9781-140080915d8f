/**
 * Test script per verificare che i campi email e telefono siano opzionali
 */

const testData = {
  // Test 1: Con email e telefono
  withBoth: {
    nome: "<PERSON>",
    cognome: "<PERSON>",
    telefono: "3331234567",
    email: "<EMAIL>",
    servizio: "Servizi CAF",
    prestazione: "",
    operatore: "Qualsiasi",
    noteAggiuntive: "",
    dataAppuntamento: "2025-01-13",
    orario: "10:00"
  },
  
  // Test 2: Solo con email
  withEmailOnly: {
    nome: "Giulia",
    cognome: "<PERSON>",
    telefono: "",
    email: "<EMAIL>",
    servizio: "Patronato",
    prestazione: "",
    operatore: "Qualsiasi",
    noteAggiuntive: "",
    dataAppuntamento: "2025-01-13",
    orario: "10:30"
  },

  // Test 3: Solo con telefono
  withPhoneOnly: {
    nome: "<PERSON>",
    cognome: "<PERSON><PERSON><PERSON>",
    telefono: "3339876543",
    email: "",
    servizio: "Avvocato",
    prestazione: "",
    operatore: "Qualsia<PERSON>",
    noteAggiuntive: "",
    dataAppuntamento: "2025-01-13",
    orario: "11:00"
  },

  // Test 4: Senza email né telefono
  withNeither: {
    nome: "Anna",
    cognome: "Neri",
    telefono: "",
    email: "",
    servizio: "Medico",
    prestazione: "",
    operatore: "Qualsiasi",
    noteAggiuntive: "",
    dataAppuntamento: "2025-01-13",
    orario: "11:30"
  }
};

async function testAPI(testName, data) {
  console.log(`\n=== Test: ${testName} ===`);
  console.log('Dati inviati:', JSON.stringify(data, null, 2));
  
  try {
    const response = await fetch('http://localhost:3000/api/send-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    const result = await response.json();
    
    console.log('Status:', response.status);
    console.log('Risposta:', JSON.stringify(result, null, 2));
    
    if (response.ok) {
      console.log('✅ Test PASSATO');
    } else {
      console.log('❌ Test FALLITO');
    }
    
  } catch (error) {
    console.log('❌ Errore di rete:', error.message);
  }
}

async function runAllTests() {
  console.log('🧪 Avvio test per campi opzionali email e telefono...\n');
  
  // Esegui tutti i test
  await testAPI('Con email e telefono', testData.withBoth);
  await testAPI('Solo con email', testData.withEmailOnly);
  await testAPI('Solo con telefono', testData.withPhoneOnly);
  await testAPI('Senza email né telefono', testData.withNeither);
  
  console.log('\n🏁 Test completati!');
}

// Esegui i test se il file viene eseguito direttamente
if (typeof window === 'undefined') {
  // Ambiente Node.js
  runAllTests().catch(console.error);
} else {
  // Ambiente browser - esponi le funzioni globalmente
  window.testOptionalFields = runAllTests;
  window.testData = testData;
  console.log('Test functions available: window.testOptionalFields(), window.testData');
}
