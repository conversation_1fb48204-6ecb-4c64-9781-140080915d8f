-- Simple update script to add break_disabled field
-- Run this step by step if you encounter issues with the full script

-- Step 1: Add break_disabled column to employee_availability table
ALTER TABLE employee_availability 
ADD COLUMN IF NOT EXISTS break_disabled BOOLEAN DEFAULT false;

-- Step 2: Add break_disabled column to employee_special_schedules table
ALTER TABLE employee_special_schedules 
ADD COLUMN IF NOT EXISTS break_disabled BOOLEAN DEFAULT false;

-- Step 3: Drop existing constraints
ALTER TABLE employee_availability DROP CONSTRAINT IF EXISTS valid_break_times;
ALTER TABLE employee_special_schedules DROP CONSTRAINT IF EXISTS valid_special_break_times;

-- Step 4: Add new constraints that consider break_disabled
ALTER TABLE employee_availability 
ADD CONSTRAINT valid_break_times CHECK (
    break_disabled = true OR
    (break_start_time IS NULL AND break_end_time IS NULL) OR
    (break_start_time IS NOT NULL AND break_end_time IS NOT NULL AND 
     break_end_time > break_start_time AND
     break_start_time >= start_time AND break_end_time <= end_time)
);

ALTER TABLE employee_special_schedules 
ADD CONSTRAINT valid_special_break_times CHECK (
    break_disabled = true OR
    (break_start_time IS NULL AND break_end_time IS NULL) OR
    (break_start_time IS NOT NULL AND break_end_time IS NOT NULL AND 
     break_end_time > break_start_time AND
     break_start_time >= start_time AND break_end_time <= end_time)
);

-- Step 5: Drop and recreate the view
DROP VIEW IF EXISTS employee_availability_view;

CREATE VIEW employee_availability_view AS
SELECT 
    ea.id,
    ea.employee_id,
    e.name as employee_name,
    ea.day_of_week,
    CASE ea.day_of_week
        WHEN 0 THEN 'Domenica'
        WHEN 1 THEN 'Lunedì'
        WHEN 2 THEN 'Martedì'
        WHEN 3 THEN 'Mercoledì'
        WHEN 4 THEN 'Giovedì'
        WHEN 5 THEN 'Venerdì'
        WHEN 6 THEN 'Sabato'
    END as day_name,
    ea.start_time,
    ea.end_time,
    ea.break_start_time,
    ea.break_end_time,
    ea.break_disabled,
    ea.is_available,
    ea.notes,
    ea.created_at,
    ea.updated_at
FROM employee_availability ea
JOIN employees e ON ea.employee_id = e.id
WHERE e.is_active = true
ORDER BY e.name, ea.day_of_week, ea.start_time;

-- Step 6: Update the availability checking function
CREATE OR REPLACE FUNCTION is_employee_available(
    p_employee_id UUID,
    p_date DATE,
    p_time TIME
) RETURNS BOOLEAN AS $$
DECLARE
    v_day_of_week INTEGER;
    v_special_schedule RECORD;
    v_regular_availability RECORD;
BEGIN
    -- Get day of week (0=Sunday, 1=Monday, etc.)
    v_day_of_week := EXTRACT(DOW FROM p_date);
    
    -- Check for special schedule first (overrides regular availability)
    SELECT * INTO v_special_schedule
    FROM employee_special_schedules
    WHERE employee_id = p_employee_id AND date = p_date;
    
    IF FOUND THEN
        -- Special schedule exists
        IF NOT v_special_schedule.is_available THEN
            RETURN FALSE;
        END IF;
        
        -- Check if time is within special schedule hours
        IF p_time < v_special_schedule.start_time OR p_time >= v_special_schedule.end_time THEN
            RETURN FALSE;
        END IF;
        
        -- Check if time is during break (only if break is not disabled)
        IF NOT COALESCE(v_special_schedule.break_disabled, false) AND
           v_special_schedule.break_start_time IS NOT NULL AND 
           p_time >= v_special_schedule.break_start_time AND 
           p_time < v_special_schedule.break_end_time THEN
            RETURN FALSE;
        END IF;
        
        RETURN TRUE;
    END IF;
    
    -- No special schedule, check regular availability
    SELECT * INTO v_regular_availability
    FROM employee_availability
    WHERE employee_id = p_employee_id 
      AND day_of_week = v_day_of_week
      AND is_available = true
      AND p_time >= start_time 
      AND p_time < end_time;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Check if time is during break (only if break is not disabled)
    IF NOT COALESCE(v_regular_availability.break_disabled, false) AND
       v_regular_availability.break_start_time IS NOT NULL AND 
       p_time >= v_regular_availability.break_start_time AND 
       p_time < v_regular_availability.break_end_time THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Clean up data where break is disabled
UPDATE employee_availability 
SET break_start_time = NULL, break_end_time = NULL 
WHERE break_disabled = true AND (break_start_time IS NOT NULL OR break_end_time IS NOT NULL);

UPDATE employee_special_schedules 
SET break_start_time = NULL, break_end_time = NULL 
WHERE break_disabled = true AND (break_start_time IS NOT NULL OR break_end_time IS NOT NULL);

-- Step 8: Grant permissions
GRANT SELECT ON employee_availability_view TO anon, authenticated;
