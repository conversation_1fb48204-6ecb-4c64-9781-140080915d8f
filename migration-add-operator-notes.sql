-- Migration script to add new columns to existing appointments table
-- Run this in your Supabase SQL Editor if you already have the appointments table

-- Add operatore column
ALTER TABLE appointments 
ADD COLUMN IF NOT EXISTS operatore VARCHAR(50) DEFAULT 'Qualsiasi';

-- Add note_aggiuntive column
ALTER TABLE appointments 
ADD COLUMN IF NOT EXISTS note_aggiuntive TEXT;

-- Update existing records to have default operatore value
UPDATE appointments 
SET operatore = 'Qualsiasi' 
WHERE operatore IS NULL;

-- Add comment for documentation
COMMENT ON COLUMN appointments.operatore IS 'Preferred operator for the appointment';
COMMENT ON COLUMN appointments.note_aggiuntive IS 'Additional notes for the appointment';
