import { NextResponse } from 'next/server';
import { authenticateAdmin } from '../../../../../lib/supabaseAuthUtils.js';
import { 
  getEmployeeById, 
  updateEmployee, 
  deleteEmployee 
} from '../../../../../lib/supabaseEmployeeUtils.js';

/**
 * GET /api/admin/employees/[id]
 * Get a specific employee by ID
 */
export async function GET(request, { params }) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: 'ID dipendente richiesto' },
        { status: 400 }
      );
    }

    const employee = await getEmployeeById(id);

    if (!employee) {
      return NextResponse.json(
        { success: false, message: 'Dipendente non trovato' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: employee
    });

  } catch (error) {
    console.error('Error in GET /api/admin/employees/[id]:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Errore interno del server',
        error: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/employees/[id]
 * Update a specific employee
 */
export async function PUT(request, { params }) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const employeeData = await request.json();

    if (!id) {
      return NextResponse.json(
        { success: false, message: 'ID dipendente richiesto' },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!employeeData.name || employeeData.name.trim() === '') {
      return NextResponse.json(
        { success: false, message: 'Il nome del dipendente è obbligatorio' },
        { status: 400 }
      );
    }

    // Validate email format if provided
    if (employeeData.email && employeeData.email.trim() !== '') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(employeeData.email)) {
        return NextResponse.json(
          { success: false, message: 'Formato email non valido' },
          { status: 400 }
        );
      }
    }

    // Check if employee exists
    const existingEmployee = await getEmployeeById(id);
    if (!existingEmployee) {
      return NextResponse.json(
        { success: false, message: 'Dipendente non trovato' },
        { status: 404 }
      );
    }

    // Update the employee
    const updatedEmployee = await updateEmployee(id, employeeData);

    return NextResponse.json({
      success: true,
      message: 'Dipendente aggiornato con successo',
      data: updatedEmployee
    });

  } catch (error) {
    console.error('Error in PUT /api/admin/employees/[id]:', error);
    
    // Handle specific database errors
    if (error.message.includes('duplicate key value violates unique constraint')) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Un dipendente con questa email esiste già' 
        },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        message: 'Errore nell\'aggiornamento del dipendente',
        error: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/employees/[id]
 * Delete a specific employee
 */
export async function DELETE(request, { params }) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: 'ID dipendente richiesto' },
        { status: 400 }
      );
    }

    // Check if employee exists
    const existingEmployee = await getEmployeeById(id);
    if (!existingEmployee) {
      return NextResponse.json(
        { success: false, message: 'Dipendente non trovato' },
        { status: 404 }
      );
    }

    // Prevent deletion of "Qualsiasi" employee
    if (existingEmployee.name === 'Qualsiasi') {
      return NextResponse.json(
        { success: false, message: 'Non è possibile eliminare l\'operatore "Qualsiasi"' },
        { status: 400 }
      );
    }

    // Delete the employee
    await deleteEmployee(id);

    return NextResponse.json({
      success: true,
      message: 'Dipendente eliminato con successo'
    });

  } catch (error) {
    console.error('Error in DELETE /api/admin/employees/[id]:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Errore nell\'eliminazione del dipendente',
        error: error.message 
      },
      { status: 500 }
    );
  }
}
